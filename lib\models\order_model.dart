class Order {
  final String id;
  final String clientName;
  final DateTime orderDate;
  final DateTime? dueDate;
  final double amount;
  final OrderStatus status;
  final String? description;
  final List<String>? attachments;

  Order({
    required this.id,
    required this.clientName,
    required this.orderDate,
    this.dueDate,
    required this.amount,
    required this.status,
    this.description,
    this.attachments,
  });

  // Méthode pour créer une copie modifiée de l'objet
  Order copyWith({
    String? id,
    String? clientName,
    DateTime? orderDate,
    DateTime? dueDate,
    double? amount,
    OrderStatus? status,
    String? description,
    List<String>? attachments,
  }) {
    return Order(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      orderDate: orderDate ?? this.orderDate,
      dueDate: dueDate ?? this.dueDate,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      description: description ?? this.description,
      attachments: attachments ?? this.attachments,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientName': clientName,
      'orderDate': orderDate.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'amount': amount,
      'status': status.index,
      'description': description,
      'attachments': attachments,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      clientName: map['clientName'],
      orderDate: DateTime.fromMillisecondsSinceEpoch(map['orderDate']),
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      amount: map['amount'],
      status: OrderStatus.values[map['status']],
      description: map['description'],
      attachments: map['attachments'] != null
          ? List<String>.from(map['attachments'])
          : null,
    );
  }
}

enum OrderStatus {
  planned,   // Planifiée
  inProgress, // En cours
  delayed,    // En retard
  completed,  // Terminée
  cancelled   // Annulée
}