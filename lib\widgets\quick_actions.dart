import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;
  final Color? color;

  const QuickActionButton({
    super.key,
    required this.icon,
    required this.label,
    this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? AppTheme.primaryColor;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton circulaire
        Container(
          width: 56,
          height: 56,
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                buttonColor,
                buttonColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: buttonColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(28),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
        // Libellé du bouton
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey[800],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class QuickActionsPanel extends StatelessWidget {
  final VoidCallback? onNewOrder;
  final VoidCallback? onNewProject;
  final VoidCallback? onNewInvoice;
  final VoidCallback? onNewTask;

  const QuickActionsPanel({
    super.key,
    this.onNewOrder,
    this.onNewProject,
    this.onNewInvoice,
    this.onNewTask,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: AppTheme.softShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          QuickActionButton(
            icon: Icons.shopping_bag,
            label: 'Nouvelle\ncommande',
            onPressed: onNewOrder,
            color: AppTheme.primaryColor,
          ),
          QuickActionButton(
            icon: Icons.brush,
            label: 'Ajouter\nmaquette',
            onPressed: onNewProject,
            color: AppTheme.accentColor,
          ),
          QuickActionButton(
            icon: Icons.receipt,
            label: 'Nouvelle\nfacture',
            onPressed: onNewInvoice,
            color: AppTheme.secondaryColor,
          ),
          QuickActionButton(
            icon: Icons.event_note,
            label: 'Nouvelle\ntâche',
            onPressed: onNewTask,
            color: AppTheme.tertiaryColor,
          ),
        ],
      ),
    );
  }
}

class FloatingQuickActions extends StatelessWidget {
  final VoidCallback? onNewOrder;
  final VoidCallback? onNewProject;
  final VoidCallback? onNewInvoice;
  final VoidCallback? onNewTask;

  const FloatingQuickActions({
    super.key,
    this.onNewOrder,
    this.onNewProject,
    this.onNewInvoice,
    this.onNewTask,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 24,
      right: 24,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (onNewTask != null)
            _buildFloatingAction(
              icon: Icons.event_note,
              label: 'Nouvelle tâche',
              onPressed: onNewTask,
              color: AppTheme.tertiaryColor,
            ),
          if (onNewInvoice != null)
            _buildFloatingAction(
              icon: Icons.receipt,
              label: 'Nouvelle facture',
              onPressed: onNewInvoice,
              color: AppTheme.secondaryColor,
            ),
          if (onNewProject != null)
            _buildFloatingAction(
              icon: Icons.brush,
              label: 'Ajouter maquette',
              onPressed: onNewProject,
              color: AppTheme.accentColor,
            ),
          if (onNewOrder != null)
            _buildFloatingAction(
              icon: Icons.shopping_bag,
              label: 'Nouvelle commande',
              onPressed: onNewOrder,
              color: AppTheme.primaryColor,
            ),
          const SizedBox(height: 16),
          FloatingActionButton(
            onPressed: () {},
            backgroundColor: AppTheme.primaryColor,
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingAction({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Label
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Button
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onPressed,
                borderRadius: BorderRadius.circular(24),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}