import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/app_theme.dart';
import '../utils/demo_data.dart';
import '../utils/formatters.dart';
import '../widgets/invoice_form_dialog.dart';

class InvoicesScreen extends StatefulWidget {
  const InvoicesScreen({super.key});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen> {
  List<Invoice> _invoices = [];
  String _searchQuery = '';
  InvoiceStatus? _selectedStatus;
  String _sortBy = 'date'; // 'date', 'amount', 'client'
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _invoices = List.from(DemoData.invoices);
  }

  List<Invoice> get _filteredAndSortedInvoices {
    var filtered =
        _invoices.where((invoice) {
          final matchesSearch =
              invoice.clientName.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              invoice.number.toLowerCase().contains(_searchQuery.toLowerCase());
          final matchesStatus =
              _selectedStatus == null || invoice.status == _selectedStatus;
          return matchesSearch && matchesStatus;
        }).toList();

    // Tri
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'amount':
          comparison = a.totalAmount.compareTo(b.totalAmount);
          break;
        case 'client':
          comparison = a.clientName.compareTo(b.clientName);
          break;
        case 'date':
        default:
          comparison = a.issueDate.compareTo(b.issueDate);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Gestion des Factures'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showInvoiceDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                if (value == _sortBy) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
              });
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'date',
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par date'),
                        if (_sortBy == 'date') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'amount',
                    child: Row(
                      children: [
                        Icon(Icons.euro, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par montant'),
                        if (_sortBy == 'amount') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'client',
                    child: Row(
                      children: [
                        Icon(Icons.person, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par client'),
                        if (_sortBy == 'client') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche et filtres
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                // Barre de recherche
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Rechercher une facture...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Filtres par statut
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildStatusFilter('Toutes', null),
                      const SizedBox(width: 8),
                      _buildStatusFilter('Brouillon', InvoiceStatus.draft),
                      const SizedBox(width: 8),
                      _buildStatusFilter('Envoyée', InvoiceStatus.sent),
                      const SizedBox(width: 8),
                      _buildStatusFilter('Payée', InvoiceStatus.paid),
                      const SizedBox(width: 8),
                      _buildStatusFilter('En retard', InvoiceStatus.overdue),
                      const SizedBox(width: 8),
                      _buildStatusFilter('Annulée', InvoiceStatus.cancelled),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Statistiques rapides
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Total',
                    _filteredAndSortedInvoices.length.toString(),
                    Icons.receipt,
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Montant total',
                    Formatters.formatCurrency(
                      _filteredAndSortedInvoices.fold(
                        0.0,
                        (sum, invoice) => sum + invoice.totalAmount,
                      ),
                    ),
                    Icons.euro,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'En attente',
                    _filteredAndSortedInvoices
                        .where((i) => i.status == InvoiceStatus.sent)
                        .length
                        .toString(),
                    Icons.pending,
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'En retard',
                    _filteredAndSortedInvoices
                        .where((i) => i.status == InvoiceStatus.overdue)
                        .length
                        .toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ),
          // Liste des factures
          Expanded(
            child:
                _filteredAndSortedInvoices.isEmpty
                    ? const Center(
                      child: Text(
                        'Aucune facture trouvée',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredAndSortedInvoices.length,
                      itemBuilder: (context, index) {
                        final invoice = _filteredAndSortedInvoices[index];
                        return _buildInvoiceCard(invoice);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(String label, InvoiceStatus? status) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: AppTheme.primaryColor.withOpacity(0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildQuickStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invoice.number,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        invoice.clientName,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      Formatters.formatCurrency(invoice.totalAmount),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 4),
                    _buildStatusBadge(invoice.status),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'view':
                        _viewInvoice(invoice);
                        break;
                      case 'edit':
                        _showInvoiceDialog(invoice: invoice);
                        break;
                      case 'duplicate':
                        _duplicateInvoice(invoice);
                        break;
                      case 'delete':
                        _deleteInvoice(invoice);
                        break;
                      case 'send':
                        _sendInvoice(invoice);
                        break;
                      case 'mark_paid':
                        _markAsPaid(invoice);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 18),
                              SizedBox(width: 8),
                              Text('Voir'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        if (invoice.status != InvoiceStatus.sent) ...[
                          const PopupMenuItem(
                            value: 'send',
                            child: Row(
                              children: [
                                Icon(Icons.send, size: 18),
                                SizedBox(width: 8),
                                Text('Envoyer'),
                              ],
                            ),
                          ),
                        ],
                        if (invoice.status == InvoiceStatus.sent ||
                            invoice.status == InvoiceStatus.overdue) ...[
                          const PopupMenuItem(
                            value: 'mark_paid',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  size: 18,
                                  color: Colors.green,
                                ),
                                SizedBox(width: 8),
                                Text('Marquer comme payée'),
                              ],
                            ),
                          ),
                        ],
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, size: 18),
                              SizedBox(width: 8),
                              Text('Dupliquer'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Informations supplémentaires
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Émise: ${Formatters.formatDate(invoice.issueDate)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.event, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Échéance: ${Formatters.formatDate(invoice.dueDate)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            if (invoice.items != null && invoice.items!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${invoice.items!.length} article(s)',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(InvoiceStatus status) {
    Color color;
    String text;

    switch (status) {
      case InvoiceStatus.draft:
        color = Colors.grey;
        text = 'Brouillon';
        break;
      case InvoiceStatus.sent:
        color = AppTheme.accentColor;
        text = 'Envoyée';
        break;
      case InvoiceStatus.paid:
        color = Colors.green;
        text = 'Payée';
        break;
      case InvoiceStatus.overdue:
        color = Colors.red;
        text = 'En retard';
        break;
      case InvoiceStatus.cancelled:
        color = Colors.red;
        text = 'Annulée';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  void _showInvoiceDialog({Invoice? invoice}) {
    showDialog(
      context: context,
      builder:
          (context) => InvoiceFormDialog(
            invoice: invoice,
            onSave: (newInvoice) {
              setState(() {
                if (invoice == null) {
                  // Nouvelle facture
                  _invoices.add(newInvoice);
                } else {
                  // Modification
                  final index = _invoices.indexWhere((i) => i.id == invoice.id);
                  if (index != -1) {
                    _invoices[index] = newInvoice;
                  }
                }
              });
            },
          ),
    );
  }

  void _viewInvoice(Invoice invoice) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Facture ${invoice.number}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Client: ${invoice.clientName}'),
                  Text(
                    'Date d\'émission: ${Formatters.formatDate(invoice.issueDate)}',
                  ),
                  Text(
                    'Date d\'échéance: ${Formatters.formatDate(invoice.dueDate)}',
                  ),
                  Text('Statut: ${_getStatusText(invoice.status)}'),
                  const SizedBox(height: 16),
                  const Text(
                    'Articles:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ...invoice.items!.map(
                    (item) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '• ${item.description} - ${Formatters.formatCurrency(item.unitPrice)} x ${item.quantity}',
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Total: ${Formatters.formatCurrency(invoice.totalAmount)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  void _duplicateInvoice(Invoice invoice) {
    final duplicatedInvoice = invoice.copyWith(
      id: 'INV-${DateTime.now().millisecondsSinceEpoch}',
      number: 'INV-${DateTime.now().millisecondsSinceEpoch}',
      issueDate: DateTime.now(),
      dueDate: DateTime.now().add(const Duration(days: 30)),
      status: InvoiceStatus.draft,
    );

    setState(() {
      _invoices.add(duplicatedInvoice);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Facture dupliquée avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _sendInvoice(Invoice invoice) {
    setState(() {
      final index = _invoices.indexWhere((i) => i.id == invoice.id);
      if (index != -1) {
        _invoices[index] = invoice.copyWith(status: InvoiceStatus.sent);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Facture envoyée avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _markAsPaid(Invoice invoice) {
    setState(() {
      final index = _invoices.indexWhere((i) => i.id == invoice.id);
      if (index != -1) {
        _invoices[index] = invoice.copyWith(status: InvoiceStatus.paid);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Facture marquée comme payée'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteInvoice(Invoice invoice) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la facture "${invoice.number}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _invoices.removeWhere((i) => i.id == invoice.id);
                  });
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Facture supprimée avec succès'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  String _getStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Brouillon';
      case InvoiceStatus.sent:
        return 'Envoyée';
      case InvoiceStatus.paid:
        return 'Payée';
      case InvoiceStatus.overdue:
        return 'En retard';
      case InvoiceStatus.cancelled:
        return 'Annulée';
    }
  }
}
