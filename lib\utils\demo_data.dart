import 'package:flutter/material.dart';
import '../models/models.dart';

class DemoData {
  // Données de démonstration pour les commandes
  static List<Order> getOrders() {
    return [
      Order(
        id: 'ORD-001',
        clientName: 'Entreprise ABC',
        orderDate: DateTime.now().subtract(const Duration(days: 5)),
        dueDate: DateTime.now().add(const Duration(days: 10)),
        amount: 1250.0,
        status: OrderStatus.planned,
        description: 'Impression de 500 flyers recto-verso',
      ),
      Order(
        id: 'ORD-002',
        clientName: 'Restaurant Le Gourmet',
        orderDate: DateTime.now().subtract(const Duration(days: 10)),
        dueDate: DateTime.now().add(const Duration(days: 2)),
        amount: 850.0,
        status: OrderStatus.inProgress,
        description: 'Création de menus et cartes de visite',
      ),
      Order(
        id: 'ORD-003',
        clientName: 'Boutique Mode',
        orderDate: DateTime.now().subtract(const Duration(days: 15)),
        dueDate: DateTime.now().subtract(const Duration(days: 2)),
        amount: 2100.0,
        status: OrderStatus.delayed,
        description: 'Conception et impression de catalogues',
      ),
      Order(
        id: 'ORD-004',
        clientName: 'Cabinet Juridique',
        orderDate: DateTime.now().subtract(const Duration(days: 20)),
        dueDate: DateTime.now().add(const Duration(days: 5)),
        amount: 450.0,
        status: OrderStatus.inProgress,
        description: 'Impression de papier à en-tête et cartes de visite',
      ),
      Order(
        id: 'ORD-005',
        clientName: 'Association Culturelle',
        orderDate: DateTime.now().subtract(const Duration(days: 3)),
        dueDate: DateTime.now().add(const Duration(days: 15)),
        amount: 1800.0,
        status: OrderStatus.planned,
        description: 'Affiches et brochures pour événement',
      ),
      Order(
        id: 'ORD-006',
        clientName: 'Agence Immobilière',
        orderDate: DateTime.now().subtract(const Duration(days: 25)),
        dueDate: DateTime.now().subtract(const Duration(days: 5)),
        amount: 950.0,
        status: OrderStatus.completed,
        description: 'Panneaux et brochures immobilières',
      ),
      Order(
        id: 'ORD-007',
        clientName: 'École Primaire',
        orderDate: DateTime.now().subtract(const Duration(days: 12)),
        dueDate: DateTime.now().subtract(const Duration(days: 1)),
        amount: 550.0,
        status: OrderStatus.delayed,
        description: 'Impression de bulletins scolaires',
      ),
      Order(
        id: 'ORD-008',
        clientName: 'Garage Auto',
        orderDate: DateTime.now().subtract(const Duration(days: 8)),
        dueDate: DateTime.now().add(const Duration(days: 7)),
        amount: 750.0,
        status: OrderStatus.inProgress,
        description: 'Flyers promotionnels et cartes de fidélité',
      ),
      Order(
        id: 'ORD-009',
        clientName: 'Pharmacie Centrale',
        orderDate: DateTime.now().subtract(const Duration(days: 18)),
        dueDate: DateTime.now().add(const Duration(days: 3)),
        amount: 1100.0,
        status: OrderStatus.planned,
        description: 'Sacs papier personnalisés et étiquettes',
      ),
    ];
  }

  // Données de démonstration pour les projets et maquettes
  static List<Project> getProjects() {
    return [
      Project(
        id: 'PRJ-001',
        title: 'Logo Restaurant Le Gourmet',
        clientName: 'Restaurant Le Gourmet',
        startDate: DateTime.now().subtract(const Duration(days: 12)),
        dueDate: DateTime.now().add(const Duration(days: 3)),
        status: ProjectStatus.inProgress,
        progress: 0.7,
        thumbnailUrl: 'assets/images/projects/logo_restaurant.jpg',
        description:
            'Création d\'un logo moderne pour un restaurant gastronomique',
      ),
      Project(
        id: 'PRJ-002',
        title: 'Catalogue Boutique Mode',
        clientName: 'Boutique Mode',
        startDate: DateTime.now().subtract(const Duration(days: 20)),
        dueDate: DateTime.now().add(const Duration(days: 5)),
        status: ProjectStatus.validation,
        progress: 0.9,
        thumbnailUrl: 'assets/images/projects/catalogue_mode.jpg',
        description:
            'Conception d\'un catalogue de 24 pages pour la collection automne',
      ),
      Project(
        id: 'PRJ-003',
        title: 'Affiche Festival de Musique',
        clientName: 'Association Culturelle',
        startDate: DateTime.now().subtract(const Duration(days: 5)),
        dueDate: DateTime.now().add(const Duration(days: 10)),
        status: ProjectStatus.inProgress,
        progress: 0.4,
        thumbnailUrl: 'assets/images/projects/affiche_festival.jpg',
        description:
            'Création d\'une affiche A2 pour un festival de musique local',
      ),
      Project(
        id: 'PRJ-004',
        title: 'Site Web Agence Immobilière',
        clientName: 'Agence Immobilière',
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        dueDate: DateTime.now().subtract(const Duration(days: 2)),
        status: ProjectStatus.delivered,
        progress: 1.0,
        thumbnailUrl: 'assets/images/projects/site_immobilier.jpg',
        description:
            'Maquette de site web responsive pour une agence immobilière',
      ),
      Project(
        id: 'PRJ-005',
        title: 'Brochure Garage Auto',
        clientName: 'Garage Auto',
        startDate: DateTime.now().subtract(const Duration(days: 8)),
        dueDate: DateTime.now().add(const Duration(days: 12)),
        status: ProjectStatus.inProgress,
        progress: 0.3,
        thumbnailUrl: 'assets/images/projects/brochure_garage.jpg',
        description:
            'Conception d\'une brochure 3 volets présentant les services du garage',
      ),
    ];
  }

  // Données de démonstration pour les factures
  static List<Invoice> getInvoices() {
    return [
      Invoice(
        id: 'INV-001',
        number: 'FAC-2024-001',
        clientName: 'Entreprise ABC',
        issueDate: DateTime.now().subtract(const Duration(days: 15)),
        dueDate: DateTime.now().add(const Duration(days: 15)),
        amount: 1250.0,
        status: InvoiceStatus.sent,
        orderId: 'ORD-001',
      ),
      Invoice(
        id: 'INV-002',
        number: 'FAC-2024-002',
        clientName: 'Restaurant Le Gourmet',
        issueDate: DateTime.now().subtract(const Duration(days: 20)),
        dueDate: DateTime.now().subtract(const Duration(days: 5)),
        amount: 850.0,
        status: InvoiceStatus.paid,
        orderId: 'ORD-002',
      ),
      Invoice(
        id: 'INV-003',
        number: 'FAC-2024-003',
        clientName: 'Boutique Mode',
        issueDate: DateTime.now().subtract(const Duration(days: 25)),
        dueDate: DateTime.now().subtract(const Duration(days: 10)),
        amount: 2100.0,
        status: InvoiceStatus.overdue,
        orderId: 'ORD-003',
      ),
      Invoice(
        id: 'INV-004',
        number: 'FAC-2024-004',
        clientName: 'Cabinet Juridique',
        issueDate: DateTime.now().subtract(const Duration(days: 5)),
        dueDate: DateTime.now().add(const Duration(days: 25)),
        amount: 450.0,
        status: InvoiceStatus.sent,
        orderId: 'ORD-004',
      ),
      Invoice(
        id: 'INV-005',
        number: 'FAC-2024-005',
        clientName: 'Agence Immobilière',
        issueDate: DateTime.now().subtract(const Duration(days: 30)),
        dueDate: DateTime.now().subtract(const Duration(days: 15)),
        amount: 950.0,
        status: InvoiceStatus.paid,
        orderId: 'ORD-006',
      ),
    ];
  }

  // Données de démonstration pour les tâches
  static List<Task> getTasks() {
    return [
      Task(
        id: 'TSK-001',
        title: 'Réunion avec client Entreprise ABC',
        date: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(hours: 2)),
        startTime: const TimeOfDay(hour: 10, minute: 0),
        endTime: const TimeOfDay(hour: 11, minute: 30),
        status: TaskStatus.pending,
        category: TaskCategory.ceo,
        priority: TaskPriority.high,
        description: 'Discussion sur les nouveaux projets',
        relatedItemId: 'ORD-001',
        assignedTo: 'Jean Dupont',
        tags: ['réunion', 'client', 'important'],
      ),
      Task(
        id: 'TSK-002',
        title: 'Finaliser maquette Restaurant',
        date: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 1)),
        startTime: const TimeOfDay(hour: 14, minute: 0),
        endTime: const TimeOfDay(hour: 16, minute: 0),
        status: TaskStatus.inProgress,
        category: TaskCategory.design,
        priority: TaskPriority.medium,
        description: 'Ajuster les couleurs et la typographie',
        relatedItemId: 'PRJ-001',
        assignedTo: 'Marie Martin',
        tags: ['design', 'maquette', 'restaurant'],
      ),
      Task(
        id: 'TSK-003',
        title: 'Impression flyers Boutique Mode',
        date: DateTime.now().add(const Duration(days: 1)),
        startTime: const TimeOfDay(hour: 9, minute: 0),
        endTime: const TimeOfDay(hour: 12, minute: 0),
        status: TaskStatus.pending,
        category: TaskCategory.print,
        description: '500 exemplaires recto-verso',
        relatedItemId: 'ORD-003',
      ),
      Task(
        id: 'TSK-004',
        title: 'Appel fournisseur papier',
        date: DateTime.now().add(const Duration(days: 1)),
        startTime: const TimeOfDay(hour: 14, minute: 30),
        endTime: const TimeOfDay(hour: 15, minute: 0),
        status: TaskStatus.pending,
        category: TaskCategory.ceo,
        description: 'Négociation tarifs pour commande trimestrielle',
      ),
      Task(
        id: 'TSK-005',
        title: 'Création logo Pharmacie',
        date: DateTime.now().add(const Duration(days: 2)),
        startTime: const TimeOfDay(hour: 10, minute: 0),
        endTime: const TimeOfDay(hour: 13, minute: 0),
        status: TaskStatus.pending,
        category: TaskCategory.design,
        description: 'Première ébauche de 3 propositions',
        relatedItemId: 'ORD-009',
      ),
      Task(
        id: 'TSK-006',
        title: 'Relance factures impayées',
        date: DateTime.now().subtract(const Duration(days: 1)),
        startTime: const TimeOfDay(hour: 11, minute: 0),
        endTime: const TimeOfDay(hour: 12, minute: 0),
        status: TaskStatus.completed,
        category: TaskCategory.ceo,
        description: 'Contacter les clients avec factures en retard',
      ),
      Task(
        id: 'TSK-007',
        title: 'Maintenance imprimante grand format',
        date: DateTime.now().add(const Duration(days: 3)),
        isAllDay: true,
        status: TaskStatus.pending,
        category: TaskCategory.print,
        description: 'Intervention technicien pour calibrage',
      ),
    ];
  }

  // Données de démonstration pour les fichiers récents
  static List<RecentFile> getRecentFiles() {
    return [
      RecentFile(
        id: 'FILE-001',
        name: 'Logo_Restaurant_V2.psd',
        path: '/projects/restaurant/logo_v2.psd',
        type: FileType.design,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        modifiedAt: DateTime.now().subtract(const Duration(hours: 5)),
        size: 15360, // 15 Mo
        relatedItemId: 'PRJ-001',
        category: FileCategory.mockup,
      ),
      RecentFile(
        id: 'FILE-002',
        name: 'Catalogue_Mode_BAT.pdf',
        path: '/projects/boutique/catalogue_bat.pdf',
        type: FileType.pdf,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        modifiedAt: DateTime.now().subtract(const Duration(hours: 1)),
        size: 8192, // 8 Mo
        relatedItemId: 'PRJ-002',
        category: FileCategory.proofOfConcept,
      ),
      RecentFile(
        id: 'FILE-003',
        name: 'Facture_ABC_001.pdf',
        path: '/invoices/entreprise_abc_001.pdf',
        type: FileType.pdf,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        modifiedAt: DateTime.now().subtract(const Duration(days: 15)),
        size: 512, // 512 Ko
        relatedItemId: 'INV-001',
        category: FileCategory.invoice,
      ),
      RecentFile(
        id: 'FILE-004',
        name: 'Contrat_Agence_Immobiliere.docx',
        path: '/contracts/agence_immobiliere.docx',
        type: FileType.document,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        modifiedAt: DateTime.now().subtract(const Duration(days: 25)),
        size: 1024, // 1 Mo
        relatedItemId: 'ORD-006',
        category: FileCategory.contract,
      ),
      RecentFile(
        id: 'FILE-005',
        name: 'Affiche_Festival_Musique.ai',
        path: '/projects/association/affiche_festival.ai',
        type: FileType.design,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        modifiedAt: DateTime.now().subtract(const Duration(hours: 12)),
        size: 25600, // 25 Mo
        relatedItemId: 'PRJ-003',
        category: FileCategory.mockup,
      ),
      RecentFile(
        id: 'FILE-006',
        name: 'Devis_Garage_Auto.pdf',
        path: '/quotes/garage_auto.pdf',
        type: FileType.pdf,
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        modifiedAt: DateTime.now().subtract(const Duration(days: 8)),
        size: 768, // 768 Ko
        relatedItemId: 'ORD-008',
        category: FileCategory.quote,
      ),
    ];
  }

  // Données de démonstration pour les performances commerciales
  static SalesPerformance getSalesPerformance() {
    return SalesPerformance(
      clientsVisited: 12,
      ordersObtained: 8,
      amountGenerated: 32500.0,
      baseSalary: 2000.0,
      variableBonus: 1000.0,
      badge: PerformanceBadge.excellent,
      performanceBreakdown: {
        'Commandes livrées': 0.4,
        'Temps d\'activité': 0.25,
        'Objectifs atteints': 0.2,
        'Divers': 0.15,
      },
    );
  }

  // Statistiques générales pour le dashboard
  static Map<String, int> getOrderStats() {
    return {
      'total': 29,
      'planned': 13,
      'inProgress': 9,
      'delayed': 4,
      'completed': 3,
    };
  }

  // Getters statiques pour faciliter l'accès aux données
  static SalesPerformance get salesPerformance => getSalesPerformance();
  static List<Project> get projects => getProjects();
  static List<Invoice> get invoices => getInvoices();
  static List<RecentFile> get recentFiles => getRecentFiles();
  static List<Task> get tasks => getTasks();
  static Map<String, int> get orderStats => getOrderStats();
}
