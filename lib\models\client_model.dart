class Client {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String address;
  final ClientType type;
  final DateTime createdAt;
  final double totalRevenue;
  final String? notes;

  Client({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.address,
    required this.type,
    required this.createdAt,
    required this.totalRevenue,
    this.notes,
  });

  // Méthode pour créer une copie modifiée de l'objet
  Client copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    ClientType? type,
    DateTime? createdAt,
    double? totalRevenue,
    String? notes,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      notes: notes ?? this.notes,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'type': type.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'totalRevenue': totalRevenue,
      'notes': notes,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id'],
      name: map['name'],
      email: map['email'],
      phone: map['phone'],
      address: map['address'],
      type: ClientType.values[map['type']],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      totalRevenue: map['totalRevenue'],
      notes: map['notes'],
    );
  }
}

enum ClientType {
  individual, // Particulier
  company,    // Entreprise
}
