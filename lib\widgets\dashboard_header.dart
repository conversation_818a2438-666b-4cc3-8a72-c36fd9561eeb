import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';

class DashboardHeader extends StatelessWidget {
  final String userName;
  final DateTime? selectedDate;
  final Function(DateTime)? onDateChanged;
  final VoidCallback? onNotificationsPressed;
  final VoidCallback? onCallsPressed;
  final VoidCallback? onMessagesPressed;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onSettingsPressed;
  final VoidCallback? onProfilePressed;
  final String? userAvatarUrl;
  final int notificationCount;

  const DashboardHeader({
    super.key,
    required this.userName,
    this.selectedDate,
    this.onDateChanged,
    this.onNotificationsPressed,
    this.onCallsPressed,
    this.onMessagesPressed,
    this.onSearchPressed,
    this.onSettingsPressed,
    this.onProfilePressed,
    this.userAvatarUrl,
    this.notificationCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final date = selectedDate ?? now;
    final dateFormat = DateFormat('d MMMM yyyy', 'fr_FR');
    final formattedDate = dateFormat.format(date);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: AppTheme.softShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Message d'accueil
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bonjour $userName 👋',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              // Sélecteur de date
              InkWell(
                onTap: () => _selectDate(context),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.arrow_drop_down, size: 16),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Icônes de navigation
          Row(
            children: [
              _buildIconButton(
                icon: Icons.notifications,
                label: 'Rappels',
                onPressed: onNotificationsPressed,
                badgeCount: notificationCount,
              ),
              _buildIconButton(
                icon: Icons.phone,
                label: 'Appels',
                onPressed: onCallsPressed,
              ),
              _buildIconButton(
                icon: Icons.message,
                label: 'Messages',
                onPressed: onMessagesPressed,
              ),
              _buildIconButton(
                icon: Icons.search,
                label: 'Recherche',
                onPressed: onSearchPressed,
              ),
              _buildIconButton(
                icon: Icons.settings,
                label: 'Paramètres',
                onPressed: onSettingsPressed,
              ),
              const SizedBox(width: 8),
              // Avatar de l'utilisateur
              InkWell(
                onTap: onProfilePressed,
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.5),
                      width: 2,
                    ),
                  ),
                  child: userAvatarUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Image.network(
                            userAvatarUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Text(
                                  userName.isNotEmpty
                                      ? userName[0].toUpperCase()
                                      : '?',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                              );
                            },
                          ),
                        )
                      : Center(
                          child: Text(
                            userName.isNotEmpty
                                ? userName[0].toUpperCase()
                                : '?',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
    int badgeCount = 0,
  }) {
    return Tooltip(
      message: label,
      child: Stack(
        children: [
          IconButton(
            icon: Icon(icon),
            onPressed: onPressed,
            color: Colors.grey[700],
            tooltip: label,
          ),
          if (badgeCount > 0)
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Center(
                  child: Text(
                    badgeCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (onDateChanged == null) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: const Locale('fr', 'FR'),
    );

    if (picked != null && picked != selectedDate) {
      onDateChanged!(picked);
    }
  }
}