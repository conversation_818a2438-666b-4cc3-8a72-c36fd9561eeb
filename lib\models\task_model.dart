import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class Task {
  final String id;
  final String title;
  final DateTime date;
  final DateTime? dueDate; // Date d'échéance
  final TimeOfDay? startTime;
  final TimeOfDay? endTime;
  final TaskStatus status;
  final TaskCategory category;
  final TaskPriority priority; // Priorité de la tâche
  final String? description;
  final String? relatedItemId; // ID d'une commande, projet, etc. associé
  final String? assignedTo; // Personne assignée à la tâche
  final List<String>? tags; // Tags associés à la tâche
  final bool isAllDay;

  Task({
    required this.id,
    required this.title,
    required this.date,
    this.dueDate,
    this.startTime,
    this.endTime,
    required this.status,
    required this.category,
    this.priority = TaskPriority.medium,
    this.description,
    this.relatedItemId,
    this.assignedTo,
    this.tags,
    this.isAllDay = false,
  });

  // Méthode pour obtenir la couleur associée à la catégorie de tâche
  Color get categoryColor {
    switch (category) {
      case TaskCategory.ceo:
        return AppTheme.taskCEOColor;
      case TaskCategory.design:
        return AppTheme.taskDesignColor;
      case TaskCategory.print:
        return AppTheme.taskPrintColor;
    }
  }

  // Méthode pour obtenir l'icône associée à la catégorie de tâche
  IconData get categoryIcon {
    switch (category) {
      case TaskCategory.ceo:
        return Icons.business;
      case TaskCategory.design:
        return Icons.design_services;
      case TaskCategory.print:
        return Icons.print;
    }
  }

  // Méthode pour obtenir l'icône associée au statut de la tâche
  IconData get statusIcon {
    switch (status) {
      case TaskStatus.todo:
        return Icons.radio_button_unchecked;
      case TaskStatus.pending:
        return Icons.schedule;
      case TaskStatus.inProgress:
        return Icons.hourglass_bottom;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.overdue:
        return Icons.warning;
    }
  }

  // Méthode pour créer une copie modifiée de l'objet
  Task copyWith({
    String? id,
    String? title,
    DateTime? date,
    DateTime? dueDate,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    TaskStatus? status,
    TaskCategory? category,
    TaskPriority? priority,
    String? description,
    String? relatedItemId,
    String? assignedTo,
    List<String>? tags,
    bool? isAllDay,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      description: description ?? this.description,
      relatedItemId: relatedItemId ?? this.relatedItemId,
      assignedTo: assignedTo ?? this.assignedTo,
      tags: tags ?? this.tags,
      isAllDay: isAllDay ?? this.isAllDay,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'date': date.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'startTime':
          startTime != null
              ? {'hour': startTime!.hour, 'minute': startTime!.minute}
              : null,
      'endTime':
          endTime != null
              ? {'hour': endTime!.hour, 'minute': endTime!.minute}
              : null,
      'status': status.index,
      'category': category.index,
      'priority': priority.index,
      'description': description,
      'relatedItemId': relatedItemId,
      'assignedTo': assignedTo,
      'tags': tags,
      'isAllDay': isAllDay,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory Task.fromMap(Map<String, dynamic> map) {
    return Task(
      id: map['id'],
      title: map['title'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      dueDate:
          map['dueDate'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
              : null,
      startTime:
          map['startTime'] != null
              ? TimeOfDay(
                hour: map['startTime']['hour'],
                minute: map['startTime']['minute'],
              )
              : null,
      endTime:
          map['endTime'] != null
              ? TimeOfDay(
                hour: map['endTime']['hour'],
                minute: map['endTime']['minute'],
              )
              : null,
      status: TaskStatus.values[map['status']],
      category: TaskCategory.values[map['category']],
      priority:
          TaskPriority.values[map['priority'] ?? TaskPriority.medium.index],
      description: map['description'],
      relatedItemId: map['relatedItemId'],
      assignedTo: map['assignedTo'],
      tags: map['tags'] != null ? List<String>.from(map['tags']) : null,
      isAllDay: map['isAllDay'] ?? false,
    );
  }
}

enum TaskStatus {
  todo, // À faire
  pending, // En attente
  inProgress, // En cours
  completed, // Terminée
  overdue, // En retard
}

enum TaskCategory {
  ceo, // Tâches CEO (noir)
  design, // Tâches graphisme (violet)
  print, // Tâches impression (orange)
}

enum TaskPriority {
  low, // Basse
  medium, // Moyenne
  high, // Haute
  urgent, // Urgente
}
