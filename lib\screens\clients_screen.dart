import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';
import '../widgets/client_form_dialog.dart';

class ClientsScreen extends StatefulWidget {
  const ClientsScreen({super.key});

  @override
  State<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends State<ClientsScreen> {
  List<Client> _clients = [];
  String _searchQuery = '';
  ClientType? _selectedType;
  String _sortBy = 'name'; // 'name', 'createdAt', 'totalRevenue'
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _loadClients();
  }

  void _loadClients() {
    // Créer des clients fictifs basés sur les données existantes
    _clients = [
      Client(
        id: 'client-1',
        name: 'Restaurant Le Gourmet',
        email: '<EMAIL>',
        phone: '+33 1 23 45 67 89',
        address: '15 Rue de la Paix, 75001 Paris',
        type: ClientType.company,
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        totalRevenue: 15420.50,
        notes: 'Client premium, commandes régulières',
      ),
      Client(
        id: 'client-2',
        name: 'Jean Dupont',
        email: '<EMAIL>',
        phone: '+33 6 12 34 56 78',
        address: '42 Avenue des Champs, 69000 Lyon',
        type: ClientType.individual,
        createdAt: DateTime.now().subtract(const Duration(days: 85)),
        totalRevenue: 3250.00,
        notes: 'Particulier, projets web',
      ),
      Client(
        id: 'client-3',
        name: 'TechCorp Solutions',
        email: '<EMAIL>',
        phone: '+33 4 56 78 90 12',
        address: '8 Boulevard Innovation, 06000 Nice',
        type: ClientType.company,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        totalRevenue: 28750.75,
        notes: 'Entreprise technologique, contrats long terme',
      ),
      Client(
        id: 'client-4',
        name: 'Marie Martin',
        email: '<EMAIL>',
        phone: '+33 7 89 01 23 45',
        address: '23 Rue du Commerce, 33000 Bordeaux',
        type: ClientType.individual,
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        totalRevenue: 1890.25,
        notes: 'Consultante indépendante',
      ),
      Client(
        id: 'client-5',
        name: 'Garage Moderne SARL',
        email: '<EMAIL>',
        phone: '+33 2 34 56 78 90',
        address: '67 Route Nationale, 44000 Nantes',
        type: ClientType.company,
        createdAt: DateTime.now().subtract(const Duration(days: 160)),
        totalRevenue: 12340.00,
        notes: 'Garage automobile, facturations mensuelles',
      ),
    ];
  }

  List<Client> get _filteredAndSortedClients {
    var filtered =
        _clients.where((client) {
          final matchesSearch =
              client.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              client.email.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (client.phone?.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ??
                  false);
          final matchesType =
              _selectedType == null || client.type == _selectedType;
          return matchesSearch && matchesType;
        }).toList();

    // Tri
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'totalRevenue':
          comparison = a.totalRevenue.compareTo(b.totalRevenue);
          break;
        case 'name':
        default:
          comparison = a.name.compareTo(b.name);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Gestion des Clients'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showClientDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                if (value == _sortBy) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
              });
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'name',
                    child: Row(
                      children: [
                        Icon(Icons.sort_by_alpha, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par nom'),
                        if (_sortBy == 'name') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'createdAt',
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par date'),
                        if (_sortBy == 'createdAt') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'totalRevenue',
                    child: Row(
                      children: [
                        Icon(Icons.euro, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par CA'),
                        if (_sortBy == 'totalRevenue') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche et filtres
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                // Barre de recherche
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Rechercher un client...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Filtres par type
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildTypeFilter('Tous', null),
                      const SizedBox(width: 8),
                      _buildTypeFilter('Particuliers', ClientType.individual),
                      const SizedBox(width: 8),
                      _buildTypeFilter('Entreprises', ClientType.company),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Statistiques rapides
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Total',
                    _filteredAndSortedClients.length.toString(),
                    Icons.people,
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Particuliers',
                    _filteredAndSortedClients
                        .where((c) => c.type == ClientType.individual)
                        .length
                        .toString(),
                    Icons.person,
                    AppTheme.accentColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Entreprises',
                    _filteredAndSortedClients
                        .where((c) => c.type == ClientType.company)
                        .length
                        .toString(),
                    Icons.business,
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'CA Total',
                    Formatters.formatCurrency(
                      _filteredAndSortedClients.fold(
                        0.0,
                        (sum, client) => sum + client.totalRevenue,
                      ),
                    ),
                    Icons.euro,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ),
          // Liste des clients
          Expanded(
            child:
                _filteredAndSortedClients.isEmpty
                    ? const Center(
                      child: Text(
                        'Aucun client trouvé',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredAndSortedClients.length,
                      itemBuilder: (context, index) {
                        final client = _filteredAndSortedClients[index];
                        return _buildClientCard(client);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeFilter(String label, ClientType? type) {
    final isSelected = _selectedType == type;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildQuickStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildClientCard(Client client) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor:
                      client.type == ClientType.company
                          ? AppTheme.primaryColor
                          : AppTheme.accentColor,
                  child: Icon(
                    client.type == ClientType.company
                        ? Icons.business
                        : Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        client.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        client.email,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildTypeBadge(client.type),
                    const SizedBox(height: 4),
                    Text(
                      Formatters.formatCurrency(client.totalRevenue),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'view':
                        _viewClient(client);
                        break;
                      case 'edit':
                        _showClientDialog(client: client);
                        break;
                      case 'duplicate':
                        _duplicateClient(client);
                        break;
                      case 'delete':
                        _deleteClient(client);
                        break;
                      case 'create_project':
                        _createProjectForClient(client);
                        break;
                      case 'create_invoice':
                        _createInvoiceForClient(client);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 18),
                              SizedBox(width: 8),
                              Text('Voir détails'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'create_project',
                          child: Row(
                            children: [
                              Icon(
                                Icons.add_task,
                                size: 18,
                                color: Colors.blue,
                              ),
                              SizedBox(width: 8),
                              Text('Nouveau projet'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'create_invoice',
                          child: Row(
                            children: [
                              Icon(
                                Icons.receipt,
                                size: 18,
                                color: Colors.green,
                              ),
                              SizedBox(width: 8),
                              Text('Nouvelle facture'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, size: 18),
                              SizedBox(width: 8),
                              Text('Dupliquer'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Informations de contact
            Row(
              children: [
                if (client.phone != null) ...[
                  Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    client.phone!,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                ],
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    client.address,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Client depuis: ${Formatters.formatDate(client.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            if (client.notes != null && client.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(Icons.note, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        client.notes!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTypeBadge(ClientType type) {
    Color color;
    String text;
    IconData icon;

    switch (type) {
      case ClientType.individual:
        color = AppTheme.accentColor;
        text = 'Particulier';
        icon = Icons.person;
        break;
      case ClientType.company:
        color = AppTheme.primaryColor;
        text = 'Entreprise';
        icon = Icons.business;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showClientDialog({Client? client}) {
    showDialog(
      context: context,
      builder:
          (context) => ClientFormDialog(
            client: client,
            onSave: (newClient) {
              setState(() {
                if (client == null) {
                  // Nouveau client
                  _clients.add(newClient);
                } else {
                  // Modification
                  final index = _clients.indexWhere((c) => c.id == client.id);
                  if (index != -1) {
                    _clients[index] = newClient;
                  }
                }
              });
            },
          ),
    );
  }

  void _viewClient(Client client) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails - ${client.name}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow(
                    'Type',
                    client.type == ClientType.company
                        ? 'Entreprise'
                        : 'Particulier',
                  ),
                  _buildDetailRow('Email', client.email),
                  if (client.phone != null)
                    _buildDetailRow('Téléphone', client.phone!),
                  _buildDetailRow('Adresse', client.address),
                  _buildDetailRow(
                    'Client depuis',
                    Formatters.formatDate(client.createdAt),
                  ),
                  _buildDetailRow(
                    'Chiffre d\'affaires',
                    Formatters.formatCurrency(client.totalRevenue),
                  ),
                  if (client.notes != null && client.notes!.isNotEmpty)
                    _buildDetailRow('Notes', client.notes!),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _duplicateClient(Client client) {
    final duplicatedClient = client.copyWith(
      id: 'client-${DateTime.now().millisecondsSinceEpoch}',
      name: '${client.name} (Copie)',
      email: 'copie.${client.email}',
      createdAt: DateTime.now(),
      totalRevenue: 0.0,
    );

    setState(() {
      _clients.add(duplicatedClient);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Client dupliqué avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _createProjectForClient(Client client) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Création d\'un projet pour ${client.name}'),
        backgroundColor: Colors.blue,
        action: SnackBarAction(
          label: 'Aller aux projets',
          textColor: Colors.white,
          onPressed: () {
            // Navigation vers l'écran des projets
          },
        ),
      ),
    );
  }

  void _createInvoiceForClient(Client client) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Création d\'une facture pour ${client.name}'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Aller aux factures',
          textColor: Colors.white,
          onPressed: () {
            // Navigation vers l'écran des factures
          },
        ),
      ),
    );
  }

  void _deleteClient(Client client) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le client "${client.name}" ?\n\nCette action supprimera également tous les projets et factures associés.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _clients.removeWhere((c) => c.id == client.id);
                  });
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Client supprimé avec succès'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
