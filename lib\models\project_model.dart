class Project {
  final String id;
  final String title;
  final String clientName;
  final DateTime startDate;
  final DateTime? dueDate;
  final ProjectStatus status;
  final double progress; // Progression de 0 à 1 (0% à 100%)
  final String? thumbnailUrl;
  final String? description;
  final List<String>? attachments;

  Project({
    required this.id,
    required this.title,
    required this.clientName,
    required this.startDate,
    this.dueDate,
    required this.status,
    required this.progress,
    this.thumbnailUrl,
    this.description,
    this.attachments,
  });

  // Méthode pour créer une copie modifiée de l'objet
  Project copyWith({
    String? id,
    String? title,
    String? clientName,
    DateTime? startDate,
    DateTime? dueDate,
    ProjectStatus? status,
    double? progress,
    String? thumbnailUrl,
    String? description,
    List<String>? attachments,
  }) {
    return Project(
      id: id ?? this.id,
      title: title ?? this.title,
      clientName: clientName ?? this.clientName,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      description: description ?? this.description,
      attachments: attachments ?? this.attachments,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'clientName': clientName,
      'startDate': startDate.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'status': status.index,
      'progress': progress,
      'thumbnailUrl': thumbnailUrl,
      'description': description,
      'attachments': attachments,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory Project.fromMap(Map<String, dynamic> map) {
    return Project(
      id: map['id'],
      title: map['title'],
      clientName: map['clientName'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['startDate']),
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      status: ProjectStatus.values[map['status']],
      progress: map['progress'],
      thumbnailUrl: map['thumbnailUrl'],
      description: map['description'],
      attachments: map['attachments'] != null
          ? List<String>.from(map['attachments'])
          : null,
    );
  }
}

enum ProjectStatus {
  inProgress,  // En cours
  validation,  // En validation
  delivered,   // Livré
  archived     // Archivé
}