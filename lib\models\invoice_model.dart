class Invoice {
  final String id;
  final String clientName;
  final DateTime issueDate;
  final DateTime dueDate;
  final double amount;
  final InvoiceStatus status;
  final String? orderId; // Référence à une commande associée
  final String? notes;
  final List<InvoiceItem>? items;

  Invoice({
    required this.id,
    required this.clientName,
    required this.issueDate,
    required this.dueDate,
    required this.amount,
    required this.status,
    this.orderId,
    this.notes,
    this.items,
  });

  // Méthode pour créer une copie modifiée de l'objet
  Invoice copyWith({
    String? id,
    String? clientName,
    DateTime? issueDate,
    DateTime? dueDate,
    double? amount,
    InvoiceStatus? status,
    String? orderId,
    String? notes,
    List<InvoiceItem>? items,
  }) {
    return Invoice(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      orderId: orderId ?? this.orderId,
      notes: notes ?? this.notes,
      items: items ?? this.items,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientName': clientName,
      'issueDate': issueDate.millisecondsSinceEpoch,
      'dueDate': dueDate.millisecondsSinceEpoch,
      'amount': amount,
      'status': status.index,
      'orderId': orderId,
      'notes': notes,
      'items': items?.map((item) => item.toMap()).toList(),
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'],
      clientName: map['clientName'],
      issueDate: DateTime.fromMillisecondsSinceEpoch(map['issueDate']),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      amount: map['amount'],
      status: InvoiceStatus.values[map['status']],
      orderId: map['orderId'],
      notes: map['notes'],
      items: map['items'] != null
          ? List<InvoiceItem>.from(
              map['items'].map((item) => InvoiceItem.fromMap(item)))
          : null,
    );
  }
}

class InvoiceItem {
  final String description;
  final int quantity;
  final double unitPrice;
  final double? taxRate; // Taux de TVA (en pourcentage)

  InvoiceItem({
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.taxRate,
  });

  // Calcul du montant total de l'élément (quantité * prix unitaire)
  double get totalBeforeTax => quantity * unitPrice;

  // Calcul du montant de la taxe
  double get taxAmount => taxRate != null ? totalBeforeTax * (taxRate! / 100) : 0;

  // Calcul du montant total avec taxe
  double get totalWithTax => totalBeforeTax + taxAmount;

  // Méthode pour convertir l'objet en Map
  Map<String, dynamic> toMap() {
    return {
      'description': description,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'taxRate': taxRate,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      description: map['description'],
      quantity: map['quantity'],
      unitPrice: map['unitPrice'],
      taxRate: map['taxRate'],
    );
  }
}

enum InvoiceStatus {
  draft,     // Brouillon
  sent,      // Envoyée
  paid,      // Payée
  overdue,   // En retard
  cancelled  // Annulée
}

// Classe pour les devis, qui est similaire à une facture mais avec un statut différent
class Quote extends Invoice {
  final QuoteStatus quoteStatus;

  Quote({
    required super.id,
    required super.clientName,
    required super.issueDate,
    required super.dueDate,
    required super.amount,
    required super.status,
    required this.quoteStatus,
    super.orderId,
    super.notes,
    super.items,
  });

  @override
  Quote copyWith({
    String? id,
    String? clientName,
    DateTime? issueDate,
    DateTime? dueDate,
    double? amount,
    InvoiceStatus? status,
    QuoteStatus? quoteStatus,
    String? orderId,
    String? notes,
    List<InvoiceItem>? items,
  }) {
    return Quote(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      quoteStatus: quoteStatus ?? this.quoteStatus,
      orderId: orderId ?? this.orderId,
      notes: notes ?? this.notes,
      items: items ?? this.items,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map['quoteStatus'] = quoteStatus.index;
    return map;
  }

  factory Quote.fromMap(Map<String, dynamic> map) {
    return Quote(
      id: map['id'],
      clientName: map['clientName'],
      issueDate: DateTime.fromMillisecondsSinceEpoch(map['issueDate']),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      amount: map['amount'],
      status: InvoiceStatus.values[map['status']],
      quoteStatus: QuoteStatus.values[map['quoteStatus']],
      orderId: map['orderId'],
      notes: map['notes'],
      items: map['items'] != null
          ? List<InvoiceItem>.from(
              map['items'].map((item) => InvoiceItem.fromMap(item)))
          : null,
    );
  }
}

enum QuoteStatus {
  pending,    // En attente
  accepted,   // Accepté
  rejected,   // Refusé
  expired     // Expiré
}