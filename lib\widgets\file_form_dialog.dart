import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/file_model.dart';

class FileFormDialog extends StatefulWidget {
  final FileModel? file;
  final Function(FileModel) onSave;

  const FileFormDialog({super.key, this.file, required this.onSave});

  @override
  State<FileFormDialog> createState() => _FileFormDialogState();
}

class _FileFormDialogState extends State<FileFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _pathController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  final _clientIdController = TextEditingController();
  final _projectIdController = TextEditingController();
  final _invoiceIdController = TextEditingController();

  FileCategory _selectedCategory = FileCategory.other;
  double _fileSize = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.file != null) {
      _nameController.text = widget.file!.name;
      _pathController.text = widget.file!.path;
      _descriptionController.text = widget.file!.description ?? '';
      _tagsController.text = widget.file!.tags.join(', ');
      _clientIdController.text = widget.file!.clientId ?? '';
      _projectIdController.text = widget.file!.projectId ?? '';
      _invoiceIdController.text = widget.file!.invoiceId ?? '';
      _selectedCategory = widget.file!.category;
      _fileSize = widget.file!.size;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _pathController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    _clientIdController.dispose();
    _projectIdController.dispose();
    _invoiceIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.file == null ? Icons.upload_file : Icons.edit,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.file == null
                          ? 'Nouveau Fichier'
                          : 'Modifier le Fichier',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            // Contenu du formulaire
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Upload de fichier (simulation)
                      if (widget.file == null) ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.grey[300]!,
                              style: BorderStyle.solid,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.grey[50],
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.cloud_upload,
                                size: 48,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Glissez-déposez un fichier ici ou',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              ElevatedButton(
                                onPressed: _selectFile,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryColor,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Choisir un fichier'),
                              ),
                              if (_fileSize > 0) ...[
                                const SizedBox(height: 12),
                                Text(
                                  'Fichier sélectionné: ${_formatFileSize(_fileSize)}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.green[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                      // Nom du fichier
                      _buildTextField(
                        controller: _nameController,
                        label: 'Nom du fichier',
                        icon: Icons.description,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le nom du fichier est obligatoire';
                          }
                          if (value.trim().length < 3) {
                            return 'Le nom doit contenir au moins 3 caractères';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Catégorie
                      const Text(
                        'Catégorie',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[50],
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<FileCategory>(
                            value: _selectedCategory,
                            isExpanded: true,
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value!;
                              });
                            },
                            items:
                                FileCategory.values.map((category) {
                                  return DropdownMenuItem(
                                    value: category,
                                    child: Row(
                                      children: [
                                        Icon(
                                          _getCategoryIcon(category),
                                          size: 18,
                                          color: _getCategoryColor(category),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(_getCategoryText(category)),
                                      ],
                                    ),
                                  );
                                }).toList(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Chemin
                      _buildTextField(
                        controller: _pathController,
                        label: 'Chemin du fichier',
                        icon: Icons.folder,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le chemin est obligatoire';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // Description
                      _buildTextField(
                        controller: _descriptionController,
                        label: 'Description (optionnel)',
                        icon: Icons.note,
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),
                      // Tags
                      _buildTextField(
                        controller: _tagsController,
                        label: 'Tags (séparés par des virgules)',
                        icon: Icons.label,
                        validator: null,
                      ),
                      const SizedBox(height: 20),
                      // Associations
                      const Text(
                        'Associations (optionnel)',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildTextField(
                              controller: _clientIdController,
                              label: 'ID Client',
                              icon: Icons.person,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildTextField(
                              controller: _projectIdController,
                              label: 'ID Projet',
                              icon: Icons.work,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _invoiceIdController,
                        label: 'ID Facture',
                        icon: Icons.receipt,
                      ),
                      const SizedBox(height: 24),
                      // Informations supplémentaires pour modification
                      if (widget.file != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Informations fichier',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(
                                    Icons.storage,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Taille: ${_formatFileSize(widget.file!.size)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Uploadé le: ${_formatDate(widget.file!.uploadDate)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            // Boutons d'action
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: BorderSide(color: Colors.grey[400]!),
                      ),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveFile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        elevation: 0,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(
                                widget.file == null ? 'Uploader' : 'Modifier',
                              ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  void _selectFile() {
    // Simulation de sélection de fichier
    setState(() {
      _fileSize =
          1024 * 1024 +
          (DateTime.now().millisecondsSinceEpoch % 1000000); // Taille aléatoire
      if (_nameController.text.isEmpty) {
        _nameController.text =
            'nouveau_fichier_${DateTime.now().millisecondsSinceEpoch}.pdf';
      }
      if (_pathController.text.isEmpty) {
        _pathController.text = '/uploads/${_nameController.text}';
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fichier sélectionné (simulation)'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _saveFile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (widget.file == null && _fileSize == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner un fichier'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulation d'upload/sauvegarde
    await Future.delayed(const Duration(milliseconds: 1000));

    final tags =
        _tagsController.text
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();

    final now = DateTime.now();
    final file = FileModel(
      id: widget.file?.id ?? 'file-${now.millisecondsSinceEpoch}',
      name: _nameController.text.trim(),
      path: _pathController.text.trim(),
      type: _getFileTypeFromExtension(_nameController.text.trim()),
      createdAt: widget.file?.createdAt ?? now,
      modifiedAt: now,
      size: widget.file?.size ?? _fileSize,
      category: _selectedCategory,
      description:
          _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
      tags: tags,
      clientId:
          _clientIdController.text.trim().isEmpty
              ? null
              : _clientIdController.text.trim(),
      projectId:
          _projectIdController.text.trim().isEmpty
              ? null
              : _projectIdController.text.trim(),
      invoiceId:
          _invoiceIdController.text.trim().isEmpty
              ? null
              : _invoiceIdController.text.trim(),
    );

    widget.onSave(file);

    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.file == null
                ? 'Fichier uploadé avec succès'
                : 'Fichier modifié avec succès',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Color _getCategoryColor(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return Colors.blue;
      case FileCategory.contract:
        return Colors.green;
      case FileCategory.mockup:
        return Colors.purple;
      case FileCategory.invoice:
        return Colors.orange;
      case FileCategory.quote:
        return Colors.red;
      case FileCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return Icons.description;
      case FileCategory.contract:
        return Icons.gavel;
      case FileCategory.mockup:
        return Icons.palette;
      case FileCategory.invoice:
        return Icons.receipt;
      case FileCategory.quote:
        return Icons.request_quote;
      case FileCategory.other:
        return Icons.folder;
    }
  }

  String _getCategoryText(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return 'BAT (Bon à Tirer)';
      case FileCategory.contract:
        return 'Contrat';
      case FileCategory.mockup:
        return 'Maquette';
      case FileCategory.invoice:
        return 'Facture';
      case FileCategory.quote:
        return 'Devis';
      case FileCategory.other:
        return 'Autre';
    }
  }

  FileType _getFileTypeFromExtension(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return FileType.pdf;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return FileType.image;
      case 'doc':
      case 'docx':
      case 'txt':
      case 'rtf':
        return FileType.document;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return FileType.spreadsheet;
      case 'ppt':
      case 'pptx':
        return FileType.presentation;
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return FileType.archive;
      case 'ai':
      case 'psd':
      case 'fig':
      case 'sketch':
        return FileType.design;
      default:
        return FileType.other;
    }
  }

  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toInt()} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
