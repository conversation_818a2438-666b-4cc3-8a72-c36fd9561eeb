import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../theme/app_theme.dart';
import '../models/sales_performance_model.dart';

class Performance<PERSON>hart extends StatelessWidget {
  final Map<String, double> performanceData;
  final double size;

  const PerformanceChart({
    super.key,
    required this.performanceData,
    this.size = 200,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: size,
      width: size,
      child: Stack(
        children: [
          PieChart(
            PieChartData(
              sectionsSpace: 2,
              centerSpaceRadius: size / 5,
              sections: _getSections(),
              borderData: FlBorderData(show: false),
            ),
          ),
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Performance',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(performanceData.values.reduce((a, b) => a + b) * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _getSections() {
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
      Colors.grey,
    ];

    int index = 0;
    performanceData.forEach((key, value) {
      final isSelected = index == 0; // Premier élément sélectionné
      final section = PieChartSectionData(
        color: colors[index % colors.length],
        value: value,
        title: '${(value * 100).toInt()}%',
        radius: isSelected ? size / 2.5 : size / 3,
        titleStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: const [Shadow(color: Colors.black, blurRadius: 2)],
        ),
        badgeWidget:
            isSelected
                ? _Badge(
                  key.split(' ')[0],
                  size: 16,
                  borderColor: colors[index % colors.length],
                )
                : null,
        badgePositionPercentageOffset: 1.1,
      );
      sections.add(section);
      index++;
    });

    return sections;
  }
}

class _Badge extends StatelessWidget {
  final String text;
  final double size;
  final Color borderColor;

  const _Badge(this.text, {required this.size, required this.borderColor});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: PieChart.defaultDuration,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(color: borderColor, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      padding: EdgeInsets.all(size * 0.15),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            fontSize: size * 0.4,
            fontWeight: FontWeight.bold,
            color: borderColor,
          ),
        ),
      ),
    );
  }
}

class PerformanceStatsCard extends StatelessWidget {
  final SalesPerformance performance;

  const PerformanceStatsCard({super.key, required this.performance});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.cardColor1,
            AppTheme.cardColor4,
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Card(
        elevation: 0,
        color: Colors.transparent,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.blueVibrant],
                      ),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                    ),
                    child: const Icon(
                      Icons.trending_up,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'Performance de la commerciale',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.darkColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: PerformanceChart(
                    performanceData: performance.performanceBreakdown,
                    size: 180,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatItem(
                        'Clients visités',
                        performance.clientsVisited.toString(),
                        Icons.people,
                      ),
                      const SizedBox(height: 16),
                      _buildStatItem(
                        'Commandes obtenues',
                        performance.ordersObtained.toString(),
                        Icons.shopping_cart,
                      ),
                      const SizedBox(height: 16),
                      _buildStatItem(
                        'Montant généré',
                        '${performance.amountGenerated.toStringAsFixed(0)} €',
                        Icons.euro,
                      ),
                      const SizedBox(height: 16),
                      _buildStatItem(
                        'Salaire estimé',
                        '${performance.totalSalary.toStringAsFixed(0)} €',
                        Icons.account_balance_wallet,
                      ),
                      const SizedBox(height: 16),
                      _buildPerformanceBadge(performance.badge),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    // Couleurs différentes pour chaque type de statistique
    Color itemColor;
    switch (icon) {
      case Icons.people:
        itemColor = AppTheme.blueVibrant;
        break;
      case Icons.shopping_cart:
        itemColor = AppTheme.greenVibrant;
        break;
      case Icons.euro:
        itemColor = AppTheme.orangeVibrant;
        break;
      case Icons.account_balance_wallet:
        itemColor = AppTheme.purpleVibrant;
        break;
      default:
        itemColor = AppTheme.primaryColor;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            itemColor.withValues(alpha: 0.1),
            itemColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: itemColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: itemColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Icon(icon, color: itemColor, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.darkColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceBadge(PerformanceBadge badge) {
    IconData icon;
    String text;
    Color color;

    switch (badge) {
      case PerformanceBadge.topWeek:
        icon = Icons.local_fire_department;
        text = 'Top semaine';
        color = Colors.orange;
        break;
      case PerformanceBadge.excellent:
        icon = Icons.star;
        text = 'Excellent';
        color = Colors.amber;
        break;
      case PerformanceBadge.good:
        icon = Icons.thumb_up;
        text = 'Bon';
        color = Colors.green;
        break;
      case PerformanceBadge.average:
        icon = Icons.thumbs_up_down;
        text = 'Moyen';
        color = Colors.blue;
        break;
      case PerformanceBadge.belowTarget:
        icon = Icons.warning;
        text = 'Objectif non atteint';
        color = Colors.red;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
