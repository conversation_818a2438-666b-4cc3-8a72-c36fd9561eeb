import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';
import '../widgets/file_form_dialog.dart';

class FilesScreen extends StatefulWidget {
  const FilesScreen({super.key});

  @override
  State<FilesScreen> createState() => _FilesScreenState();
}

class _FilesScreenState extends State<FilesScreen> {
  List<FileModel> _files = [];
  String _searchQuery = '';
  FileCategory? _selectedCategory;
  String _sortBy = 'name'; // 'name', 'uploadDate', 'size'
  bool _sortAscending = true;
  bool _isGridView = false;

  @override
  void initState() {
    super.initState();
    _loadFiles();
  }

  void _loadFiles() {
    // Créer des fichiers fictifs
    final now = DateTime.now();
    _files = [
      FileModel(
        id: 'file-1',
        name: 'Contrat Restaurant Le Gourmet.pdf',
        path: '/documents/contracts/contrat_restaurant_legourmet.pdf',
        type: FileType.pdf,
        createdAt: now.subtract(const Duration(days: 15)),
        modifiedAt: now.subtract(const Duration(days: 15)),
        size: 2048576, // 2MB
        category: FileCategory.contract,
        description: 'Contrat de prestation pour le restaurant Le Gourmet',
        tags: ['contrat', 'restaurant', 'prestation'],
        clientId: 'client-1',
        projectId: 'project-1',
      ),
      FileModel(
        id: 'file-2',
        name: 'Logo TechCorp.ai',
        path: '/assets/logos/logo_techcorp.ai',
        type: FileType.image,
        createdAt: now.subtract(const Duration(days: 8)),
        modifiedAt: now.subtract(const Duration(days: 8)),
        size: 512000, // 500KB
        category: FileCategory.mockup,
        description: 'Logo vectoriel pour TechCorp Solutions',
        tags: ['logo', 'design', 'vectoriel'],
        clientId: 'client-3',
        projectId: 'project-2',
      ),
      FileModel(
        id: 'file-3',
        name: 'Facture_2024_001.pdf',
        path: '/invoices/2024/facture_2024_001.pdf',
        type: FileType.pdf,
        createdAt: now.subtract(const Duration(days: 3)),
        modifiedAt: now.subtract(const Duration(days: 3)),
        size: 156789,
        category: FileCategory.invoice,
        description: 'Facture pour développement site web',
        tags: ['facture', '2024', 'développement'],
        clientId: 'client-2',
        invoiceId: 'invoice-1',
      ),
      FileModel(
        id: 'file-4',
        name: 'Cahier des charges - Site immobilier.docx',
        path: '/documents/specifications/cdc_site_immobilier.docx',
        type: FileType.document,
        createdAt: now.subtract(const Duration(days: 25)),
        modifiedAt: now.subtract(const Duration(days: 25)),
        size: 1024000, // 1MB
        category: FileCategory.other,
        description: 'Spécifications techniques pour le site immobilier',
        tags: ['cahier des charges', 'immobilier', 'spécifications'],
        clientId: 'client-4',
        projectId: 'project-3',
      ),
      FileModel(
        id: 'file-5',
        name: 'Maquette_App_Mobile.fig',
        path: '/designs/mobile/maquette_app_mobile.fig',
        type: FileType.design,
        createdAt: now.subtract(const Duration(days: 12)),
        modifiedAt: now.subtract(const Duration(days: 12)),
        size: 3145728, // 3MB
        category: FileCategory.mockup,
        description: 'Maquettes UI/UX pour application mobile',
        tags: ['maquette', 'mobile', 'ui', 'ux'],
        clientId: 'client-3',
        projectId: 'project-4',
      ),
      FileModel(
        id: 'file-6',
        name: 'Backup_Database_20241201.sql',
        path: '/backups/database/backup_20241201.sql',
        type: FileType.other,
        createdAt: now.subtract(const Duration(days: 1)),
        modifiedAt: now.subtract(const Duration(days: 1)),
        size: 10485760, // 10MB
        category: FileCategory.other,
        description: 'Sauvegarde de la base de données',
        tags: ['backup', 'database', 'sauvegarde'],
      ),
      FileModel(
        id: 'file-7',
        name: 'Présentation_Projet.pptx',
        path: '/presentations/presentation_projet.pptx',
        type: FileType.presentation,
        createdAt: now.subtract(const Duration(days: 7)),
        modifiedAt: now.subtract(const Duration(days: 7)),
        size: 5242880, // 5MB
        category: FileCategory.other,
        description: 'Présentation du projet pour le client',
        tags: ['présentation', 'projet', 'client'],
        clientId: 'client-1',
        projectId: 'project-1',
      ),
    ];
  }

  List<FileModel> get _filteredAndSortedFiles {
    var filtered =
        _files.where((file) {
          final matchesSearch =
              file.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (file.description?.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ??
                  false) ||
              file.tags.any(
                (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
              );
          final matchesCategory =
              _selectedCategory == null || file.category == _selectedCategory;
          return matchesSearch && matchesCategory;
        }).toList();

    // Tri
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'uploadDate':
          comparison = a.uploadDate.compareTo(b.uploadDate);
          break;
        case 'size':
          comparison = a.size.compareTo(b.size);
          break;
        case 'name':
        default:
          comparison = a.name.compareTo(b.name);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Gestion des Fichiers'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showFileDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                if (value == _sortBy) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
              });
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'name',
                    child: Row(
                      children: [
                        Icon(Icons.sort_by_alpha, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par nom'),
                        if (_sortBy == 'name') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'uploadDate',
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par date'),
                        if (_sortBy == 'uploadDate') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'size',
                    child: Row(
                      children: [
                        Icon(Icons.storage, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par taille'),
                        if (_sortBy == 'size') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche et filtres
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                // Barre de recherche
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Rechercher un fichier...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Filtres par catégorie
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCategoryFilter('Tous', null),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('BAT', FileCategory.proofOfConcept),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('Contrats', FileCategory.contract),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('Maquettes', FileCategory.mockup),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('Factures', FileCategory.invoice),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('Devis', FileCategory.quote),
                      const SizedBox(width: 8),
                      _buildCategoryFilter('Autres', FileCategory.other),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Statistiques rapides
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Total',
                    _filteredAndSortedFiles.length.toString(),
                    Icons.folder,
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Taille',
                    _formatFileSize(
                      _filteredAndSortedFiles.fold(
                        0.0,
                        (sum, file) => sum + file.size,
                      ),
                    ),
                    Icons.storage,
                    AppTheme.accentColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Récents',
                    _filteredAndSortedFiles
                        .where(
                          (f) =>
                              DateTime.now().difference(f.uploadDate).inDays <=
                              7,
                        )
                        .length
                        .toString(),
                    Icons.schedule,
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Designs',
                    _filteredAndSortedFiles
                        .where((f) => f.category == FileCategory.mockup)
                        .length
                        .toString(),
                    Icons.palette,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ),
          // Liste/Grille des fichiers
          Expanded(
            child:
                _filteredAndSortedFiles.isEmpty
                    ? const Center(
                      child: Text(
                        'Aucun fichier trouvé',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                    : _isGridView
                    ? _buildGridView()
                    : _buildListView(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(String label, FileCategory? category) {
    final isSelected = _selectedCategory == category;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildQuickStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredAndSortedFiles.length,
      itemBuilder: (context, index) {
        final file = _filteredAndSortedFiles[index];
        return _buildFileCard(file);
      },
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _filteredAndSortedFiles.length,
      itemBuilder: (context, index) {
        final file = _filteredAndSortedFiles[index];
        return _buildFileGridCard(file);
      },
    );
  }

  Widget _buildFileCard(FileModel file) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Icône du fichier
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(
                      file.category,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getCategoryIcon(file.category),
                    color: _getCategoryColor(file.category),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        file.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatFileSize(file.size),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildCategoryBadge(file.category),
                    const SizedBox(height: 4),
                    Text(
                      Formatters.formatDate(file.uploadDate),
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleFileAction(value, file),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'download',
                          child: Row(
                            children: [
                              Icon(Icons.download, size: 18),
                              SizedBox(width: 8),
                              Text('Télécharger'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 18),
                              SizedBox(width: 8),
                              Text('Voir détails'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, size: 18),
                              SizedBox(width: 8),
                              Text('Dupliquer'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'share',
                          child: Row(
                            children: [
                              Icon(Icons.share, size: 18, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Partager'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            if (file.description != null && file.description!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                file.description!,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            if (file.tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children:
                    file.tags
                        .take(3)
                        .map(
                          (tag) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tag,
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileGridCard(FileModel file) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _getCategoryColor(
                        file.category,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        _getCategoryIcon(file.category),
                        color: _getCategoryColor(file.category),
                        size: 32,
                      ),
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleFileAction(value, file),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'download',
                          child: Row(
                            children: [
                              Icon(Icons.download, size: 18),
                              SizedBox(width: 8),
                              Text('Télécharger'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                  icon: const Icon(Icons.more_vert, size: 18),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              file.name,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatFileSize(file.size),
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const Spacer(),
            Row(
              children: [Expanded(child: _buildCategoryBadge(file.category))],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBadge(FileCategory category) {
    final color = _getCategoryColor(category);
    final text = _getCategoryText(category);
    final icon = _getCategoryIcon(category);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return Colors.blue;
      case FileCategory.contract:
        return Colors.green;
      case FileCategory.mockup:
        return Colors.purple;
      case FileCategory.invoice:
        return Colors.orange;
      case FileCategory.quote:
        return Colors.teal;
      case FileCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return Icons.check_circle;
      case FileCategory.contract:
        return Icons.gavel;
      case FileCategory.mockup:
        return Icons.palette;
      case FileCategory.invoice:
        return Icons.receipt;
      case FileCategory.quote:
        return Icons.request_quote;
      case FileCategory.other:
        return Icons.description;
    }
  }

  String _getCategoryText(FileCategory category) {
    switch (category) {
      case FileCategory.proofOfConcept:
        return 'BAT';
      case FileCategory.contract:
        return 'Contrat';
      case FileCategory.mockup:
        return 'Maquette';
      case FileCategory.invoice:
        return 'Facture';
      case FileCategory.quote:
        return 'Devis';
      case FileCategory.other:
        return 'Autre';
    }
  }

  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toInt()} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _handleFileAction(String action, FileModel file) {
    switch (action) {
      case 'download':
        _downloadFile(file);
        break;
      case 'view':
        _viewFile(file);
        break;
      case 'edit':
        _showFileDialog(file: file);
        break;
      case 'duplicate':
        _duplicateFile(file);
        break;
      case 'share':
        _shareFile(file);
        break;
      case 'delete':
        _deleteFile(file);
        break;
    }
  }

  void _showFileDialog({FileModel? file}) {
    showDialog(
      context: context,
      builder:
          (context) => FileFormDialog(
            file: file,
            onSave: (newFile) {
              setState(() {
                if (file == null) {
                  _files.add(newFile);
                } else {
                  final index = _files.indexWhere((f) => f.id == file.id);
                  if (index != -1) {
                    _files[index] = newFile;
                  }
                }
              });
            },
          ),
    );
  }

  void _downloadFile(FileModel file) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Téléchargement de ${file.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewFile(FileModel file) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails - ${file.name}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('Nom', file.name),
                  _buildDetailRow('Taille', _formatFileSize(file.size)),
                  _buildDetailRow('Catégorie', _getCategoryText(file.category)),
                  _buildDetailRow(
                    'Date d\'upload',
                    Formatters.formatDate(file.uploadDate),
                  ),
                  _buildDetailRow('Chemin', file.path),
                  if (file.description != null && file.description!.isNotEmpty)
                    _buildDetailRow('Description', file.description!),
                  if (file.tags.isNotEmpty)
                    _buildDetailRow('Tags', file.tags.join(', ')),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _duplicateFile(FileModel file) {
    final now = DateTime.now();
    final duplicatedFile = file.copyWith(
      id: 'file-${now.millisecondsSinceEpoch}',
      name: 'Copie de ${file.name}',
      modifiedAt: now,
    );

    setState(() {
      _files.add(duplicatedFile);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fichier dupliqué avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareFile(FileModel file) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Partage de ${file.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _deleteFile(FileModel file) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le fichier "${file.name}" ?\n\nCette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _files.removeWhere((f) => f.id == file.id);
                  });
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Fichier supprimé avec succès'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
