import 'package:flutter/material.dart';
import '../models/invoice_model.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';

class InvoiceList extends StatelessWidget {
  final List<Invoice> invoices;
  final Function(Invoice)? onInvoiceTap;
  final VoidCallback? onCreateNew;

  const InvoiceList({
    super.key,
    required this.invoices,
    this.onInvoiceTap,
    this.onCreateNew,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Dernières factures',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            if (onCreateNew != null)
              ElevatedButton.icon(
                onPressed: onCreateNew,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Créer une nouvelle facture'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusSmall,
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            boxShadow: AppTheme.softShadow,
          ),
          child: Column(
            children: [
              // En-tête du tableau
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Row(
                  children: [
                    const Expanded(
                      flex: 3,
                      child: Text(
                        'Client',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Montant',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Date',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: const Text(
                          'Statut',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              // Liste des factures
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: invoices.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final invoice = invoices[index];
                  return InkWell(
                    onTap:
                        onInvoiceTap != null
                            ? () => onInvoiceTap!(invoice)
                            : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Text(
                              invoice.clientName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              Formatters.formatCurrency(invoice.amount),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              Formatters.formatShortDate(invoice.issueDate),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Container(
                              alignment: Alignment.centerRight,
                              child: _buildStatusBadge(invoice.status),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(InvoiceStatus status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case InvoiceStatus.draft:
        color = Colors.grey;
        text = 'Brouillon';
        icon = Icons.edit;
        break;
      case InvoiceStatus.sent:
        color = AppTheme.warningColor;
        text = 'Envoyée';
        icon = Icons.send;
        break;
      case InvoiceStatus.paid:
        color = AppTheme.successColor;
        text = 'Payée';
        icon = Icons.check_circle;
        break;
      case InvoiceStatus.overdue:
        color = AppTheme.errorColor;
        text = 'En retard';
        icon = Icons.warning;
        break;
      case InvoiceStatus.cancelled:
        color = Colors.grey;
        text = 'Annulée';
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.5), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class QuoteList extends StatelessWidget {
  final List<Quote> quotes;
  final Function(Quote)? onQuoteTap;
  final VoidCallback? onCreateNew;

  const QuoteList({
    super.key,
    required this.quotes,
    this.onQuoteTap,
    this.onCreateNew,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Derniers devis',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            if (onCreateNew != null)
              ElevatedButton.icon(
                onPressed: onCreateNew,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Créer un nouveau devis'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppTheme.borderRadiusSmall,
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            boxShadow: AppTheme.softShadow,
          ),
          child: Column(
            children: [
              // En-tête du tableau
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Row(
                  children: [
                    const Expanded(
                      flex: 3,
                      child: Text(
                        'Client',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Montant',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Date',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: const Text(
                          'Statut',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              // Liste des devis
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: quotes.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final quote = quotes[index];
                  return InkWell(
                    onTap: onQuoteTap != null ? () => onQuoteTap!(quote) : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Text(
                              quote.clientName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              Formatters.formatCurrency(quote.amount),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              Formatters.formatShortDate(quote.issueDate),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Container(
                              alignment: Alignment.centerRight,
                              child: _buildStatusBadge(quote.quoteStatus),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(QuoteStatus status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case QuoteStatus.accepted:
        color = AppTheme.successColor;
        text = 'Accepté';
        icon = Icons.check_circle;
        break;
      case QuoteStatus.pending:
        color = AppTheme.warningColor;
        text = 'En attente';
        icon = Icons.access_time;
        break;
      case QuoteStatus.rejected:
        color = AppTheme.errorColor;
        text = 'Refusé';
        icon = Icons.cancel;
        break;
      case QuoteStatus.expired:
        color = Colors.grey;
        text = 'Expiré';
        icon = Icons.timer_off;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.5), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
