import 'package:flutter/material.dart';
import '../models/file_model.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';

class RecentFilesList extends StatelessWidget {
  final List<RecentFile> files;
  final Function(RecentFile)? onFileTap;
  final Function(RecentFile)? onFileDownload;

  const RecentFilesList({
    super.key,
    required this.files,
    this.onFileTap,
    this.onFileDownload,
  });

  @override
  Widget build(BuildContext context) {
    // Regrouper les fichiers par catégorie
    final Map<FileCategory, List<RecentFile>> filesByCategory = {};
    for (var file in files) {
      if (!filesByCategory.containsKey(file.category)) {
        filesByCategory[file.category] = [];
      }
      filesByCategory[file.category]!.add(file);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Fichiers récents',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            boxShadow: AppTheme.softShadow,
          ),
          child: Column(
            children: [
              // En-tête du tableau
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    const SizedBox(width: 40), // Espace pour l'icône
                    const Expanded(
                      flex: 3,
                      child: Text(
                        'Nom du fichier',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Type',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 2,
                      child: Text(
                        'Date',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Expanded(
                      flex: 1,
                      child: Text(
                        'Taille',
                        style: TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 40), // Espace pour les actions
                  ],
                ),
              ),
              const Divider(height: 1),
              // Liste des fichiers par catégorie
              for (var entry in filesByCategory.entries) ...[                
                _buildCategoryHeader(entry.key),
                for (var file in entry.value)
                  _buildFileRow(file, context),
                if (entry.key != filesByCategory.keys.last)
                  const Divider(height: 1),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryHeader(FileCategory category) {
    String categoryName;
    Color categoryColor;

    switch (category) {
      case FileCategory.proofOfConcept:
        categoryName = 'BAT';
        categoryColor = AppTheme.accentColor;
        break;
      case FileCategory.contract:
        categoryName = 'Contrats';
        categoryColor = AppTheme.primaryColor;
        break;
      case FileCategory.mockup:
        categoryName = 'Maquettes';
        categoryColor = AppTheme.secondaryColor;
        break;
      case FileCategory.invoice:
        categoryName = 'Factures';
        categoryColor = AppTheme.successColor;
        break;
      case FileCategory.quote:
        categoryName = 'Devis';
        categoryColor = AppTheme.warningColor;
        break;
      case FileCategory.other:
        categoryName = 'Autres';
        categoryColor = Colors.grey;
        break;
    }

    return Container(
      color: categoryColor.withOpacity(0.05),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(Icons.folder, size: 16, color: categoryColor),
          const SizedBox(width: 8),
          Text(
            categoryName,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: categoryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileRow(RecentFile file, BuildContext context) {
    return InkWell(
      onTap: onFileTap != null ? () => onFileTap!(file) : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Icône du type de fichier
            SizedBox(
              width: 40,
              child: _buildFileTypeIcon(file.type),
            ),
            // Nom du fichier
            Expanded(
              flex: 3,
              child: Text(
                file.name,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Type de fichier
            Expanded(
              flex: 2,
              child: Text(
                _getFileTypeLabel(file.type),
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            // Date de modification
            Expanded(
              flex: 2,
              child: Text(
                Formatters.formatShortDate(file.modifiedAt),
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            // Taille du fichier
            Expanded(
              flex: 1,
              child: Text(
                file.formattedSize,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
            // Bouton de téléchargement
            SizedBox(
              width: 40,
              child: IconButton(
                icon: const Icon(Icons.download, size: 20),
                onPressed: onFileDownload != null
                    ? () => onFileDownload!(file)
                    : null,
                tooltip: 'Télécharger',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileTypeIcon(FileType type) {
    IconData iconData;
    Color iconColor;

    switch (type) {
      case FileType.pdf:
        iconData = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case FileType.image:
        iconData = Icons.image;
        iconColor = Colors.blue;
        break;
      case FileType.document:
        iconData = Icons.description;
        iconColor = Colors.indigo;
        break;
      case FileType.spreadsheet:
        iconData = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case FileType.presentation:
        iconData = Icons.slideshow;
        iconColor = Colors.orange;
        break;
      case FileType.archive:
        iconData = Icons.folder_zip;
        iconColor = Colors.brown;
        break;
      case FileType.design:
        iconData = Icons.brush;
        iconColor = AppTheme.accentColor;
        break;
      case FileType.other:
        iconData = Icons.insert_drive_file;
        iconColor = Colors.grey;
        break;
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(iconData, size: 18, color: iconColor),
    );
  }

  String _getFileTypeLabel(FileType type) {
    switch (type) {
      case FileType.pdf:
        return 'PDF';
      case FileType.image:
        return 'Image';
      case FileType.document:
        return 'Document';
      case FileType.spreadsheet:
        return 'Tableur';
      case FileType.presentation:
        return 'Présentation';
      case FileType.archive:
        return 'Archive';
      case FileType.design:
        return 'Design';
      case FileType.other:
        return 'Autre';
    }
  }
}