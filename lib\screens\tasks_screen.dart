import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/app_theme.dart';
import '../utils/demo_data.dart';
import '../utils/formatters.dart';
import '../widgets/task_form_dialog.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> {
  List<Task> _tasks = [];
  String _searchQuery = '';
  TaskPriority? _selectedPriority;
  TaskStatus? _selectedStatus;
  String _sortBy = 'dueDate'; // 'dueDate', 'priority', 'title'
  bool _sortAscending = true;
  bool _showCompletedTasks = true;

  @override
  void initState() {
    super.initState();
    _tasks = List.from(DemoData.tasks);
  }

  List<Task> get _filteredAndSortedTasks {
    var filtered =
        _tasks.where((task) {
          final matchesSearch =
              task.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (task.description?.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ??
                  false);
          final matchesPriority =
              _selectedPriority == null || task.priority == _selectedPriority;
          final matchesStatus =
              _selectedStatus == null || task.status == _selectedStatus;
          final showCompleted =
              _showCompletedTasks || task.status != TaskStatus.completed;
          return matchesSearch &&
              matchesPriority &&
              matchesStatus &&
              showCompleted;
        }).toList();

    // Tri
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'priority':
          comparison = _getPriorityOrder(
            a.priority,
          ).compareTo(_getPriorityOrder(b.priority));
          break;
        case 'title':
          comparison = a.title.compareTo(b.title);
          break;
        case 'dueDate':
        default:
          if (a.dueDate == null && b.dueDate == null) {
            comparison = 0;
          } else if (a.dueDate == null) {
            comparison = 1;
          } else if (b.dueDate == null) {
            comparison = -1;
          } else {
            comparison = a.dueDate!.compareTo(b.dueDate!);
          }
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  int _getPriorityOrder(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.urgent:
        return 0;
      case TaskPriority.high:
        return 1;
      case TaskPriority.medium:
        return 2;
      case TaskPriority.low:
        return 3;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Gestion des Tâches'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showTaskDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                if (value == 'toggle_completed') {
                  _showCompletedTasks = !_showCompletedTasks;
                } else if (value == _sortBy) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
              });
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'toggle_completed',
                    child: Row(
                      children: [
                        Icon(
                          _showCompletedTasks
                              ? Icons.visibility_off
                              : Icons.visibility,
                          size: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          _showCompletedTasks
                              ? 'Masquer terminées'
                              : 'Afficher terminées',
                        ),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  PopupMenuItem(
                    value: 'dueDate',
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par échéance'),
                        if (_sortBy == 'dueDate') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'priority',
                    child: Row(
                      children: [
                        Icon(Icons.priority_high, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par priorité'),
                        if (_sortBy == 'priority') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'title',
                    child: Row(
                      children: [
                        Icon(Icons.title, size: 18),
                        SizedBox(width: 8),
                        Text('Trier par titre'),
                        if (_sortBy == 'title') ...[
                          Spacer(),
                          Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche et filtres
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                // Barre de recherche
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Rechercher une tâche...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Filtres
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // Filtre par priorité
                      _buildPriorityFilter('Toutes', null),
                      const SizedBox(width: 8),
                      _buildPriorityFilter('Haute', TaskPriority.high),
                      const SizedBox(width: 8),
                      _buildPriorityFilter('Moyenne', TaskPriority.medium),
                      const SizedBox(width: 8),
                      _buildPriorityFilter('Basse', TaskPriority.low),
                      const SizedBox(width: 16),
                      // Filtre par statut
                      _buildStatusFilter('Tous', null),
                      const SizedBox(width: 8),
                      _buildStatusFilter('À faire', TaskStatus.todo),
                      const SizedBox(width: 8),
                      _buildStatusFilter('En cours', TaskStatus.inProgress),
                      const SizedBox(width: 8),
                      _buildStatusFilter('Terminée', TaskStatus.completed),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Statistiques rapides
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Total',
                    _filteredAndSortedTasks.length.toString(),
                    Icons.task,
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'À faire',
                    _filteredAndSortedTasks
                        .where((t) => t.status == TaskStatus.todo)
                        .length
                        .toString(),
                    Icons.radio_button_unchecked,
                    Colors.grey,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'En cours',
                    _filteredAndSortedTasks
                        .where((t) => t.status == TaskStatus.inProgress)
                        .length
                        .toString(),
                    Icons.pending,
                    AppTheme.warningColor,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Terminées',
                    _filteredAndSortedTasks
                        .where((t) => t.status == TaskStatus.completed)
                        .length
                        .toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ),
          // Liste des tâches
          Expanded(
            child:
                _filteredAndSortedTasks.isEmpty
                    ? const Center(
                      child: Text(
                        'Aucune tâche trouvée',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredAndSortedTasks.length,
                      itemBuilder: (context, index) {
                        final task = _filteredAndSortedTasks[index];
                        return _buildTaskCard(task);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityFilter(String label, TaskPriority? priority) {
    final isSelected = _selectedPriority == priority;
    Color color = Colors.grey;
    if (priority != null) {
      switch (priority) {
        case TaskPriority.urgent:
          color = Colors.red.shade700;
          break;
        case TaskPriority.high:
          color = Colors.red;
          break;
        case TaskPriority.medium:
          color = AppTheme.warningColor;
          break;
        case TaskPriority.low:
          color = Colors.green;
          break;
      }
    }

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedPriority = selected ? priority : null;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: color.withValues(alpha: 0.2),
      checkmarkColor: color,
    );
  }

  Widget _buildStatusFilter(String label, TaskStatus? status) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: AppTheme.accentColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.accentColor,
    );
  }

  Widget _buildQuickStat(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCard(Task task) {
    final isOverdue =
        task.dueDate != null &&
        task.dueDate!.isBefore(DateTime.now()) &&
        task.status != TaskStatus.completed;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Checkbox pour marquer comme terminé
                Checkbox(
                  value: task.status == TaskStatus.completed,
                  onChanged: (value) {
                    _toggleTaskStatus(task);
                  },
                  activeColor: Colors.green,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          decoration:
                              task.status == TaskStatus.completed
                                  ? TextDecoration.lineThrough
                                  : null,
                          color:
                              task.status == TaskStatus.completed
                                  ? Colors.grey
                                  : null,
                        ),
                      ),
                      if (task.description != null &&
                          task.description!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          task.description!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                            decoration:
                                task.status == TaskStatus.completed
                                    ? TextDecoration.lineThrough
                                    : null,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildPriorityBadge(task.priority),
                    const SizedBox(height: 4),
                    _buildStatusBadge(task.status),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showTaskDialog(task: task);
                        break;
                      case 'duplicate':
                        _duplicateTask(task);
                        break;
                      case 'delete':
                        _deleteTask(task);
                        break;
                      case 'start':
                        _startTask(task);
                        break;
                      case 'complete':
                        _completeTask(task);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        if (task.status == TaskStatus.todo) ...[
                          const PopupMenuItem(
                            value: 'start',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.play_arrow,
                                  size: 18,
                                  color: Colors.blue,
                                ),
                                SizedBox(width: 8),
                                Text('Commencer'),
                              ],
                            ),
                          ),
                        ],
                        if (task.status != TaskStatus.completed) ...[
                          const PopupMenuItem(
                            value: 'complete',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  size: 18,
                                  color: Colors.green,
                                ),
                                SizedBox(width: 8),
                                Text('Marquer terminée'),
                              ],
                            ),
                          ),
                        ],
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, size: 18),
                              SizedBox(width: 8),
                              Text('Dupliquer'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Informations supplémentaires
            Row(
              children: [
                if (task.dueDate != null) ...[
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: isOverdue ? Colors.red : Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Échéance: ${Formatters.formatDate(task.dueDate!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: isOverdue ? Colors.red : Colors.grey[600],
                      fontWeight:
                          isOverdue ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
                if (task.startTime != null || task.endTime != null) ...[
                  if (task.dueDate != null) const SizedBox(width: 16),
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${task.startTime != null ? Formatters.formatTime(task.startTime!) : '--:--'} - ${task.endTime != null ? Formatters.formatTime(task.endTime!) : '--:--'}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
            if (isOverdue) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.warning, size: 16, color: Colors.red),
                    SizedBox(width: 4),
                    Text(
                      'Tâche en retard',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(TaskPriority priority) {
    Color color;
    String text;

    switch (priority) {
      case TaskPriority.urgent:
        color = Colors.red.shade700;
        text = 'Urgente';
        break;
      case TaskPriority.high:
        color = Colors.red;
        text = 'Haute';
        break;
      case TaskPriority.medium:
        color = AppTheme.warningColor;
        text = 'Moyenne';
        break;
      case TaskPriority.low:
        color = Colors.green;
        text = 'Basse';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildStatusBadge(TaskStatus status) {
    Color color;
    String text;

    switch (status) {
      case TaskStatus.todo:
        color = Colors.grey;
        text = 'À faire';
        break;
      case TaskStatus.pending:
        color = AppTheme.warningColor;
        text = 'En attente';
        break;
      case TaskStatus.inProgress:
        color = AppTheme.accentColor;
        text = 'En cours';
        break;
      case TaskStatus.completed:
        color = Colors.green;
        text = 'Terminée';
        break;
      case TaskStatus.overdue:
        color = Colors.red;
        text = 'En retard';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  void _showTaskDialog({Task? task}) {
    showDialog(
      context: context,
      builder:
          (context) => TaskFormDialog(
            task: task,
            onSave: (newTask) {
              setState(() {
                if (task == null) {
                  // Nouvelle tâche
                  _tasks.add(newTask);
                } else {
                  // Modification
                  final index = _tasks.indexWhere((t) => t.id == task.id);
                  if (index != -1) {
                    _tasks[index] = newTask;
                  }
                }
              });
            },
          ),
    );
  }

  void _toggleTaskStatus(Task task) {
    setState(() {
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        final newStatus =
            task.status == TaskStatus.completed
                ? TaskStatus.todo
                : TaskStatus.completed;
        _tasks[index] = task.copyWith(status: newStatus);
      }
    });
  }

  void _startTask(Task task) {
    setState(() {
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = task.copyWith(status: TaskStatus.inProgress);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tâche démarrée'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _completeTask(Task task) {
    setState(() {
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = task.copyWith(status: TaskStatus.completed);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tâche terminée'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _duplicateTask(Task task) {
    final duplicatedTask = task.copyWith(
      id: 'task-${DateTime.now().millisecondsSinceEpoch}',
      title: '${task.title} (Copie)',
      status: TaskStatus.todo,
      dueDate: task.dueDate?.add(const Duration(days: 1)),
    );

    setState(() {
      _tasks.add(duplicatedTask);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tâche dupliquée avec succès'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteTask(Task task) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la tâche "${task.title}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _tasks.removeWhere((t) => t.id == task.id);
                  });
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Tâche supprimée avec succès'),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
