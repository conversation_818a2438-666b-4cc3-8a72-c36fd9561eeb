class RecentFile {
  final String id;
  final String name;
  final String path;
  final FileType type;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final double size; // Taille en Ko
  final String? relatedItemId; // ID d'une commande, projet, etc. associé
  final FileCategory category;
  final String? description;
  final List<String> tags;
  final String? clientId;
  final String? projectId;
  final String? invoiceId;

  // Alias pour uploadDate (compatibilité)
  DateTime get uploadDate => createdAt;

  RecentFile({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.createdAt,
    required this.modifiedAt,
    required this.size,
    this.relatedItemId,
    required this.category,
    this.description,
    this.tags = const [],
    this.clientId,
    this.projectId,
    this.invoiceId,
  });

  // Méthode pour obtenir l'extension du fichier
  String get extension {
    final parts = name.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  // Méthode pour obtenir l'icône associée au type de fichier
  String get iconName {
    switch (type) {
      case FileType.pdf:
        return 'pdf';
      case FileType.image:
        return 'image';
      case FileType.document:
        return 'document';
      case FileType.spreadsheet:
        return 'spreadsheet';
      case FileType.presentation:
        return 'presentation';
      case FileType.archive:
        return 'archive';
      case FileType.design:
        return 'design';
      case FileType.other:
        return 'file';
    }
  }

  // Méthode pour formater la taille du fichier
  String get formattedSize {
    if (size < 1024) {
      return '${size.toStringAsFixed(1)} Ko';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} Mo';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} Go';
    }
  }

  // Méthode pour créer une copie modifiée de l'objet
  RecentFile copyWith({
    String? id,
    String? name,
    String? path,
    FileType? type,
    DateTime? createdAt,
    DateTime? modifiedAt,
    double? size,
    String? relatedItemId,
    FileCategory? category,
    String? description,
    List<String>? tags,
    String? clientId,
    String? projectId,
    String? invoiceId,
  }) {
    return RecentFile(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      size: size ?? this.size,
      relatedItemId: relatedItemId ?? this.relatedItemId,
      category: category ?? this.category,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      clientId: clientId ?? this.clientId,
      projectId: projectId ?? this.projectId,
      invoiceId: invoiceId ?? this.invoiceId,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'type': type.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'modifiedAt': modifiedAt.millisecondsSinceEpoch,
      'size': size,
      'relatedItemId': relatedItemId,
      'category': category.index,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory RecentFile.fromMap(Map<String, dynamic> map) {
    return RecentFile(
      id: map['id'],
      name: map['name'],
      path: map['path'],
      type: FileType.values[map['type']],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      modifiedAt: DateTime.fromMillisecondsSinceEpoch(map['modifiedAt']),
      size: map['size'],
      relatedItemId: map['relatedItemId'],
      category: FileCategory.values[map['category']],
    );
  }
}

enum FileType {
  pdf,
  image,
  document,
  spreadsheet,
  presentation,
  archive,
  design,
  other,
}

enum FileCategory {
  proofOfConcept, // BAT (Bon à Tirer)
  contract, // Contrat
  mockup, // Maquette
  invoice, // Facture
  quote, // Devis
  other, // Autre
}

// Alias pour compatibilité
typedef FileModel = RecentFile;
