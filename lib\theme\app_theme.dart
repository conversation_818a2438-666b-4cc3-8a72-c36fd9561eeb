import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Couleurs principales - Style moderne et vibrant
  static const Color primaryColor = Color(0xFF667EEA); // Bleu moderne
  static const Color secondaryColor = Color(0xFFF093FB); // Rose vibrant
  static const Color accentColor = Color(0xFF4ECDC4); // Turquoise
  static const Color tertiaryColor = Color(0xFFFFE066); // Jaune vif
  static const Color darkColor = Color(0xFF2D3748); // Gris foncé moderne

  // Palette de couleurs vives et contrastantes
  static const Color blueVibrant = Color(0xFF4299E1); // Bleu vif
  static const Color pinkVibrant = Color(0xFFED64A6); // Rose vif
  static const Color purpleVibrant = Color(0xFF9F7AEA); // Violet vif
  static const Color orangeVibrant = Color(0xFFFF9500); // Orange vif
  static const Color greenVibrant = Color(0xFF48BB78); // Vert vif
  static const Color yellowVibrant = Color(0xFFECC94B); // Jaune vif

  // Couleurs pour les statuts
  static const Color successColor = Color(0xFF68D391); // Vert moderne
  static const Color warningColor = Color(0xFFF6AD55); // Orange d'avertissement
  static const Color errorColor = Color(0xFFFC8181); // Rouge moderne
  static const Color infoColor = Color(0xFF63B3ED); // Bleu info

  // Couleurs pour les cartes modulaires
  static const Color cardColor1 = Color(0xFFFFFFFF); // Blanc pur
  static const Color cardColor2 = Color(0xFFFFF5F5); // Rose très pâle
  static const Color cardColor3 = Color(0xFFF0F8FF); // Bleu très pâle
  static const Color cardColor4 = Color(0xFFF7FAFC); // Gris très pâle
  static const Color cardColor5 = Color(0xFFFFFAF0); // Orange très pâle
  static const Color cardColor6 = Color(0xFFF0FFF4); // Vert très pâle

  // Couleurs pour les tâches
  static const Color taskCEOColor = Color(0xFF2D3748); // Gris foncé
  static const Color taskDesignColor = Color(0xFF667EEA); // Bleu
  static const Color taskPrintColor = Color(0xFFFF9500); // Orange
  static const Color taskUrgentColor = Color(0xFFE53E3E); // Rouge urgent

  // Couleurs de fond - Style minimaliste
  static const Color backgroundColor = Color(0xFFF7FAFC); // Gris très clair
  static const Color surfaceColor = Color(0xFFFFFFFF); // Blanc pur
  static const Color cardSurfaceColor = Color(0xFFFEFEFE); // Blanc cassé

  // Couleurs de texte
  static const Color textColor = darkColor; // Couleur de texte principale
  static const Color textSecondaryColor = Color(0xFF718096); // Couleur de texte secondaire

  // Couleurs de bordure
  static const Color borderColor = Color(0xFFE2E8F0); // Couleur de bordure

  // Rayons des coins arrondis - Style moderne avec formes géométriques douces
  static const double borderRadiusSmall = 12.0;
  static const double borderRadiusMedium = 16.0;
  static const double borderRadiusLarge = 20.0;
  static const double borderRadiusXLarge = 28.0;
  static const double borderRadiusCircular = 50.0;

  // Ombres modernes et subtiles
  static List<BoxShadow> get softShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.04),
      blurRadius: 12,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 20,
      offset: const Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.06),
      blurRadius: 16,
      offset: const Offset(0, 3),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get vibrantShadow => [
    BoxShadow(
      color: primaryColor.withValues(alpha: 0.15),
      blurRadius: 20,
      offset: const Offset(0, 8),
      spreadRadius: 0,
    ),
  ];

  // Thème de l'application - Style moderne et vibrant
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: surfaceColor,
      background: backgroundColor,
      error: errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: darkColor,
      onBackground: darkColor,
    ),
    scaffoldBackgroundColor: backgroundColor,
    textTheme: GoogleFonts.interTextTheme().copyWith(
      headlineLarge: GoogleFonts.inter(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: darkColor,
        letterSpacing: -0.5,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: darkColor,
        letterSpacing: -0.25,
      ),
      titleLarge: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: darkColor,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: darkColor,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: darkColor,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: darkColor,
      ),
      labelLarge: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: darkColor,
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: surfaceColor,
      foregroundColor: darkColor,
      elevation: 0,
      centerTitle: false,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      iconTheme: const IconThemeData(color: darkColor),
      titleTextStyle: GoogleFonts.inter(
        color: darkColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardTheme(
      color: cardSurfaceColor,
      elevation: 0,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusLarge),
      ),
      margin: const EdgeInsets.all(8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        backgroundColor: Colors.transparent,
        side: BorderSide(color: primaryColor, width: 1.5),
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        backgroundColor: Colors.transparent,
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cardSurfaceColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: Colors.grey.shade100),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: errorColor, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: errorColor, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: GoogleFonts.inter(
        color: Colors.grey.shade500,
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      labelStyle: GoogleFonts.inter(
        color: darkColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    ),
  );
}
