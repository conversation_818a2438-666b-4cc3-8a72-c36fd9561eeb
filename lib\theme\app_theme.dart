import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Couleurs principales
  static const Color primaryColor = Color(0xFF6B5B95); // Violet pastel
  static const Color secondaryColor = Color(0xFFFFC75F); // Jaune pastel
  static const Color accentColor = Color(0xFF88BDBC); // Bleu pastel
  static const Color tertiaryColor = Color(0xFFE1BEE7); // Lavande pastel
  static const Color darkColor = Color(0xFF333333); // Noir pour texte
  
  // Couleurs pour les statuts
  static const Color successColor = Color(0xFF9DE0AD); // Vert pastel
  static const Color warningColor = Color(0xFFFFD166); // Jaune d'avertissement
  static const Color errorColor = Color(0xFFFF6B6B); // Rouge pastel
  static const Color infoColor = Color(0xFF88BDBC); // Bleu info
  
  // Couleurs pour les cartes statistiques
  static const Color cardColor1 = Color(0xFFFFFFFF); // Blanc
  static const Color cardColor2 = Color(0xFFFFF9C4); // Jaune très pâle
  static const Color cardColor3 = Color(0xFFBBDEFB); // Bleu très pâle
  static const Color cardColor4 = Color(0xFFE1BEE7); // Lavande très pâle
  
  // Couleurs pour les tâches
  static const Color taskCEOColor = Color(0xFF333333); // Noir
  static const Color taskDesignColor = Color(0xFF6B5B95); // Violet
  static const Color taskPrintColor = Color(0xFFFF9A76); // Orange
  
  // Couleurs de fond
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  
  // Rayons des coins arrondis
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;
  
  // Ombres
  static List<BoxShadow> get softShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 10,
      offset: const Offset(0, 4),
    ),
  ];
  
  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 15,
      offset: const Offset(0, 5),
    ),
  ];
  
  // Thème de l'application
  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: surfaceColor,
      background: backgroundColor,
      error: errorColor,
    ),
    scaffoldBackgroundColor: backgroundColor,
    textTheme: GoogleFonts.interTextTheme(),
    appBarTheme: const AppBarTheme(
      backgroundColor: surfaceColor,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(color: darkColor),
      titleTextStyle: TextStyle(color: darkColor, fontSize: 20, fontWeight: FontWeight.w600),
    ),
    cardTheme: CardTheme(
      color: surfaceColor,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadiusMedium)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadiusMedium)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadiusMedium)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadiusMedium)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: surfaceColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: BorderSide(color: Colors.grey.shade200),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: const BorderSide(color: primaryColor),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),
  );
}