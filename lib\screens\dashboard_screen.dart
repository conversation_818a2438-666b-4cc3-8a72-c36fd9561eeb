import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/demo_data.dart';
import '../widgets/widgets.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // Contenu principal avec défilement
            SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // En-tête du tableau de bord
                  DashboardHeader(
                    userName: 'Shalom',
                    selectedDate: _selectedDate,
                    onDateChanged: (date) {
                      setState(() {
                        _selectedDate = date;
                      });
                    },
                    notificationCount: 3,
                    onNotificationsPressed: () {},
                    onCallsPressed: () {},
                    onMessagesPressed: () {},
                    onSearchPressed: () {},
                    onSettingsPressed: () {},
                    onProfilePressed: () {},
                  ),
                  const SizedBox(height: 24),

                  // Statistiques générales (4 cartes)
                  _buildStatisticsCards(),
                  const SizedBox(height: 24),

                  // Grille principale à deux colonnes
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // Déterminer si l'écran est assez large pour deux colonnes
                      final isWideScreen = constraints.maxWidth > 900;

                      if (isWideScreen) {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Colonne gauche
                            Expanded(
                              flex: 3,
                              child: Column(
                                children: [
                                  // Performance de la commerciale
                                  PerformanceStatsCard(
                                    performance: DemoData.salesPerformance,
                                  ),
                                  const SizedBox(height: 24),

                                  // Projets et maquettes
                                  const Text(
                                    'Projets & Maquettes en cours',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  ProjectsGrid(
                                    projects: DemoData.projects,
                                    onProjectTap: (project) {},
                                    onProjectDuplicate: (project) {},
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 24),
                            // Colonne droite
                            Expanded(
                              flex: 2,
                              child: Column(
                                children: [
                                  // Factures et devis
                                  InvoiceList(
                                    invoices: DemoData.invoices,
                                    onInvoiceTap: (invoice) {},
                                    onCreateNew: () {},
                                  ),
                                  const SizedBox(height: 24),

                                  // Fichiers récents
                                  RecentFilesList(
                                    files: DemoData.recentFiles,
                                    onFileTap: (file) {},
                                    onFileDownload: (file) {},
                                  ),
                                  const SizedBox(height: 24),

                                  // Agenda et tâches
                                  WeeklyTaskCalendar(
                                    tasks: DemoData.tasks,
                                    initialDate: _selectedDate,
                                    onTaskTap: (task) {},
                                    onDateSelected: (date) {
                                      setState(() {
                                        _selectedDate = date;
                                      });
                                    },
                                    onAddTask: () {},
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      } else {
                        // Disposition en une seule colonne pour les écrans étroits
                        return Column(
                          children: [
                            // Performance de la commerciale
                            PerformanceStatsCard(
                              performance: DemoData.salesPerformance,
                            ),
                            const SizedBox(height: 24),

                            // Projets et maquettes
                            const Text(
                              'Projets & Maquettes en cours',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ProjectsGrid(
                              projects: DemoData.projects,
                              onProjectTap: (project) {},
                              onProjectDuplicate: (project) {},
                            ),
                            const SizedBox(height: 24),

                            // Factures et devis
                            InvoiceList(
                              invoices: DemoData.invoices,
                              onInvoiceTap: (invoice) {},
                              onCreateNew: () {},
                            ),
                            const SizedBox(height: 24),

                            // Fichiers récents
                            RecentFilesList(
                              files: DemoData.recentFiles,
                              onFileTap: (file) {},
                              onFileDownload: (file) {},
                            ),
                            const SizedBox(height: 24),

                            // Agenda et tâches
                            WeeklyTaskCalendar(
                              tasks: DemoData.tasks,
                              initialDate: _selectedDate,
                              onTaskTap: (task) {},
                              onDateSelected: (date) {
                                setState(() {
                                  _selectedDate = date;
                                });
                              },
                              onAddTask: () {},
                            ),
                          ],
                        );
                      }
                    },
                  ),

                  const SizedBox(height: 24),

                  // Boutons d'action rapide (en bas)
                  QuickActionsPanel(
                    onNewOrder: () {},
                    onNewProject: () {},
                    onNewInvoice: () {},
                    onNewTask: () {},
                  ),

                  // Espace en bas pour éviter que le contenu ne soit caché par les boutons flottants
                  const SizedBox(height: 80),
                ],
              ),
            ),

            // Boutons d'action rapide flottants (alternative)
            // Décommentez pour utiliser cette version au lieu du panel fixe
            /*
            FloatingQuickActions(
              onNewOrder: () {},
              onNewProject: () {},
              onNewInvoice: () {},
              onNewTask: () {},
            ),
            */
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return GridView.count(
      crossAxisCount: 4,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        StatCard(
          title: 'Toutes les commandes',
          value: DemoData.orderStats['total'] ?? 0,
          icon: Icons.shopping_bag,
          color: AppTheme.primaryColor,
          showMiniChart: true,
          chartData: const [4, 7, 5, 10, 8, 9, 12],
        ),
        StatCard(
          title: 'Commandes planifiées',
          value: DemoData.orderStats['planned'] ?? 0,
          icon: Icons.calendar_today,
          color: AppTheme.warningColor,
          showMiniChart: true,
          chartData: const [3, 5, 4, 7, 5, 8, 6],
        ),
        StatCard(
          title: 'Commandes en cours',
          value: DemoData.orderStats['inProgress'] ?? 0,
          icon: Icons.pending_actions,
          color: AppTheme.accentColor,
          showMiniChart: true,
          chartData: const [2, 4, 3, 5, 4, 6, 5],
        ),
        StatCard(
          title: 'Commandes en retard',
          value: DemoData.orderStats['delayed'] ?? 0,
          icon: Icons.warning_amber,
          color: AppTheme.tertiaryColor,
          showMiniChart: true,
          chartData: const [1, 2, 0, 3, 1, 2, 4],
        ),
      ],
    );
  }
}
