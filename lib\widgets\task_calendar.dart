import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/task_model.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';

class WeeklyTaskCalendar extends StatefulWidget {
  final List<Task> tasks;
  final DateTime? initialDate;
  final Function(Task)? onTaskTap;
  final Function(DateTime)? onDateSelected;
  final VoidCallback? onAddTask;

  const WeeklyTaskCalendar({
    super.key,
    required this.tasks,
    this.initialDate,
    this.onTaskTap,
    this.onDateSelected,
    this.onAddTask,
  });

  @override
  State<WeeklyTaskCalendar> createState() => _WeeklyTaskCalendarState();
}

class _WeeklyTaskCalendarState extends State<WeeklyTaskCalendar> {
  late DateTime _selectedDate;
  late List<DateTime> _weekDays;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();
    _generateWeekDays();
  }

  void _generateWeekDays() {
    // Trouver le premier jour de la semaine (lundi)
    final firstDayOfWeek = _selectedDate.subtract(
      Duration(days: _selectedDate.weekday - 1),
    );

    // Générer les 7 jours de la semaine
    _weekDays = List.generate(
      7,
      (index) => firstDayOfWeek.add(Duration(days: index)),
    );
  }

  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    if (widget.onDateSelected != null) {
      widget.onDateSelected!(date);
    }
  }

  void _previousWeek() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 7));
      _generateWeekDays();
    });
  }

  void _nextWeek() {
    setState(() {
      _selectedDate = _selectedDate.add(const Duration(days: 7));
      _generateWeekDays();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Agenda et Tâches',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Row(
              children: [
                // Navigation semaine précédente
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: _previousWeek,
                  tooltip: 'Semaine précédente',
                ),
                // Affichage de la semaine actuelle
                Text(
                  '${Formatters.formatShortDate(_weekDays.first)} - ${Formatters.formatShortDate(_weekDays.last)}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                // Navigation semaine suivante
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: _nextWeek,
                  tooltip: 'Semaine suivante',
                ),
                if (widget.onAddTask != null)
                  IconButton(
                    icon: const Icon(Icons.add_circle),
                    onPressed: widget.onAddTask,
                    tooltip: 'Ajouter une tâche',
                    color: AppTheme.primaryColor,
                  ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Calendrier hebdomadaire
        Container(
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            boxShadow: AppTheme.softShadow,
          ),
          child: Row(
            children:
                _weekDays.map((day) {
                  final isSelected =
                      day.day == _selectedDate.day &&
                      day.month == _selectedDate.month &&
                      day.year == _selectedDate.year;
                  final isToday =
                      day.day == DateTime.now().day &&
                      day.month == DateTime.now().month &&
                      day.year == DateTime.now().year;

                  // Compter les tâches pour ce jour
                  final taskCount =
                      widget.tasks
                          .where(
                            (task) =>
                                task.date.day == day.day &&
                                task.date.month == day.month &&
                                task.date.year == day.year,
                          )
                          .length;

                  return Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(day),
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                                  : null,
                          border:
                              isSelected
                                  ? Border.all(
                                    color: AppTheme.primaryColor,
                                    width: 1,
                                  )
                                  : null,
                          borderRadius: BorderRadius.circular(
                            AppTheme.borderRadiusSmall,
                          ),
                        ),
                        margin: const EdgeInsets.all(4),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Jour de la semaine (Lu, Ma, etc.)
                            Text(
                              DateFormat(
                                'E',
                                'fr_FR',
                              ).format(day).toUpperCase(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color:
                                    isSelected
                                        ? AppTheme.primaryColor
                                        : Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Numéro du jour
                            Container(
                              width: 28,
                              height: 28,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color:
                                    isToday
                                        ? AppTheme.primaryColor
                                        : isSelected
                                        ? AppTheme.primaryColor.withValues(
                                          alpha: 0.2,
                                        )
                                        : null,
                              ),
                              child: Center(
                                child: Text(
                                  day.day.toString(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        isToday
                                            ? Colors.white
                                            : isSelected
                                            ? AppTheme.primaryColor
                                            : Colors.black,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 2),
                            // Indicateur de tâches
                            if (taskCount > 0)
                              Container(
                                width: 18,
                                height: 18,
                                decoration: BoxDecoration(
                                  color: AppTheme.accentColor,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    taskCount.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
        const SizedBox(height: 16),
        // Liste des tâches pour le jour sélectionné
        _buildTaskList(),
      ],
    );
  }

  Widget _buildTaskList() {
    // Filtrer les tâches pour le jour sélectionné
    final tasksForDay =
        widget.tasks
            .where(
              (task) =>
                  task.date.day == _selectedDate.day &&
                  task.date.month == _selectedDate.month &&
                  task.date.year == _selectedDate.year,
            )
            .toList();

    // Trier les tâches par heure de début
    tasksForDay.sort((a, b) {
      if (a.isAllDay && !b.isAllDay) return -1;
      if (!a.isAllDay && b.isAllDay) return 1;
      if (a.isAllDay && b.isAllDay) return 0;
      if (a.startTime == null && b.startTime == null) return 0;
      if (a.startTime == null) return 1;
      if (b.startTime == null) return -1;
      return a.startTime!.hour.compareTo(b.startTime!.hour) != 0
          ? a.startTime!.hour.compareTo(b.startTime!.hour)
          : a.startTime!.minute.compareTo(b.startTime!.minute);
    });

    if (tasksForDay.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          boxShadow: AppTheme.softShadow,
        ),
        child: const Center(
          child: Text(
            'Aucune tâche pour cette journée',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: AppTheme.softShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: tasksForDay.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final task = tasksForDay[index];
          return TaskListItem(
            task: task,
            onTap:
                widget.onTaskTap != null ? () => widget.onTaskTap!(task) : null,
          );
        },
      ),
    );
  }
}

class TaskListItem extends StatelessWidget {
  final Task task;
  final VoidCallback? onTap;

  const TaskListItem({super.key, required this.task, this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Heure de la tâche
            SizedBox(
              width: 60,
              child:
                  task.isAllDay
                      ? const Text(
                        'Journée',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      )
                      : Text(
                        '${task.startTime != null ? Formatters.formatTime(task.startTime!) : '--:--'} - ${task.endTime != null ? Formatters.formatTime(task.endTime!) : '--:--'}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
            ),
            const SizedBox(width: 12),
            // Indicateur de catégorie
            Container(
              width: 4,
              height: 40,
              decoration: BoxDecoration(
                color: task.categoryColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 12),
            // Contenu de la tâche
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Titre de la tâche
                      Expanded(
                        child: Text(
                          task.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Icône de catégorie
                      Icon(
                        task.categoryIcon,
                        size: 16,
                        color: task.categoryColor,
                      ),
                    ],
                  ),
                  if (task.description != null &&
                      task.description!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      task.description!,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(width: 8),
            // Statut de la tâche
            Icon(
              task.statusIcon,
              size: 20,
              color: _getStatusColor(task.status),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return AppTheme.primaryColor;
      case TaskStatus.pending:
        return Colors.grey;
      case TaskStatus.inProgress:
        return AppTheme.warningColor;
      case TaskStatus.completed:
        return AppTheme.successColor;
      case TaskStatus.overdue:
        return AppTheme.errorColor;
    }
  }
}
