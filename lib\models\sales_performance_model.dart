class SalesPerformance {
  final int clientsVisited;
  final int ordersObtained;
  final double amountGenerated;
  final double baseSalary;
  final double variableBonus;
  final double totalSalary;
  final PerformanceBadge badge;
  final Map<String, double> performanceBreakdown; // Répartition des performances

  SalesPerformance({
    required this.clientsVisited,
    required this.ordersObtained,
    required this.amountGenerated,
    required this.baseSalary,
    required this.variableBonus,
    required this.badge,
    required this.performanceBreakdown,
  }) : totalSalary = baseSalary + variableBonus;

  // Méthode pour calculer le bonus variable en fonction des paliers
  static double calculateBonus(double amountGenerated) {
    if (amountGenerated < 10000) {
      return 0;
    } else if (amountGenerated < 20000) {
      return 500;
    } else if (amountGenerated < 50000) {
      return 1000;
    } else if (amountGenerated < 100000) {
      return 2000;
    } else {
      return 5000;
    }
  }

  // Méthode pour déterminer le badge de performance
  static PerformanceBadge determineBadge(double amountGenerated, int ordersObtained) {
    if (amountGenerated > 50000 && ordersObtained > 10) {
      return PerformanceBadge.topWeek;
    } else if (amountGenerated > 30000 && ordersObtained > 5) {
      return PerformanceBadge.excellent;
    } else if (amountGenerated > 15000 && ordersObtained > 3) {
      return PerformanceBadge.good;
    } else if (amountGenerated > 5000) {
      return PerformanceBadge.average;
    } else {
      return PerformanceBadge.belowTarget;
    }
  }

  // Méthode pour créer une copie modifiée de l'objet
  SalesPerformance copyWith({
    int? clientsVisited,
    int? ordersObtained,
    double? amountGenerated,
    double? baseSalary,
    double? variableBonus,
    PerformanceBadge? badge,
    Map<String, double>? performanceBreakdown,
  }) {
    return SalesPerformance(
      clientsVisited: clientsVisited ?? this.clientsVisited,
      ordersObtained: ordersObtained ?? this.ordersObtained,
      amountGenerated: amountGenerated ?? this.amountGenerated,
      baseSalary: baseSalary ?? this.baseSalary,
      variableBonus: variableBonus ?? this.variableBonus,
      badge: badge ?? this.badge,
      performanceBreakdown: performanceBreakdown ?? this.performanceBreakdown,
    );
  }

  // Méthode pour convertir l'objet en Map (utile pour la persistance)
  Map<String, dynamic> toMap() {
    return {
      'clientsVisited': clientsVisited,
      'ordersObtained': ordersObtained,
      'amountGenerated': amountGenerated,
      'baseSalary': baseSalary,
      'variableBonus': variableBonus,
      'totalSalary': totalSalary,
      'badge': badge.index,
      'performanceBreakdown': performanceBreakdown,
    };
  }

  // Méthode pour créer un objet à partir d'un Map
  factory SalesPerformance.fromMap(Map<String, dynamic> map) {
    return SalesPerformance(
      clientsVisited: map['clientsVisited'],
      ordersObtained: map['ordersObtained'],
      amountGenerated: map['amountGenerated'],
      baseSalary: map['baseSalary'],
      variableBonus: map['variableBonus'],
      badge: PerformanceBadge.values[map['badge']],
      performanceBreakdown: Map<String, double>.from(map['performanceBreakdown']),
    );
  }
}

enum PerformanceBadge {
  topWeek,      // Top de la semaine 🔥
  excellent,    // Excellent 🌟
  good,         // Bon 👍
  average,      // Moyen 😐
  belowTarget   // Objectif non atteint ⚠️
}