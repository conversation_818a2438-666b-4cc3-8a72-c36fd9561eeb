import 'package:flutter/material.dart';
import '../models/project_model.dart';
import '../theme/app_theme.dart';
import '../utils/formatters.dart';

class ProjectCard extends StatelessWidget {
  final Project project;
  final VoidCallback? onTap;
  final VoidCallback? onDuplicate;

  const ProjectCard({
    super.key,
    required this.project,
    this.onTap,
    this.onDuplicate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.cardSurfaceColor,
            AppTheme.cardSurfaceColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.borderColor.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Card(
        elevation: 0,
        color: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Miniature du projet avec statut en overlay
            Stack(
              children: [
                // Image de la maquette
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                    topRight: Radius.circular(AppTheme.borderRadiusLarge),
                  ),
                  child:
                      project.thumbnailUrl != null
                          ? Image.asset(
                            project.thumbnailUrl!,
                            height: 120,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 120,
                                width: double.infinity,
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                  size: 40,
                                ),
                              );
                            },
                          )
                          : Container(
                            height: 120,
                            width: double.infinity,
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.image,
                              color: Colors.grey,
                              size: 40,
                            ),
                          ),
                ),
                // Badge de statut
                Positioned(
                  top: 8,
                  right: 8,
                  child: _buildStatusBadge(project.status),
                ),
                // Bouton de duplication
                if (onDuplicate != null)
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.copy, size: 18),
                        onPressed: onDuplicate,
                        tooltip: 'Dupliquer le projet',
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                      ),
                    ),
                  ),
              ],
            ),
            // Informations du projet
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    project.title,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 16,
                      color: AppTheme.textColor,
                      letterSpacing: 0.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 6),
                  Text(
                    project.clientName,
                    style: TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // Barre de progression
                  _buildProgressBar(project.progress),
                  const SizedBox(height: 8),
                  // Date d'échéance
                  if (project.dueDate != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.blueVibrant.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today_outlined,
                            size: 14,
                            color: AppTheme.blueVibrant,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            Formatters.formatShortDate(project.dueDate!),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.blueVibrant,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(ProjectStatus status) {
    List<Color> gradientColors;
    String text;

    switch (status) {
      case ProjectStatus.pending:
        gradientColors = [AppTheme.orangeVibrant, AppTheme.orangeVibrant.withValues(alpha: 0.8)];
        text = 'En attente';
        break;
      case ProjectStatus.inProgress:
        gradientColors = [AppTheme.blueVibrant, AppTheme.blueVibrant.withValues(alpha: 0.8)];
        text = 'En cours';
        break;
      case ProjectStatus.validation:
        gradientColors = [AppTheme.purpleVibrant, AppTheme.purpleVibrant.withValues(alpha: 0.8)];
        text = 'En validation';
        break;
      case ProjectStatus.completed:
        gradientColors = [AppTheme.greenVibrant, AppTheme.greenVibrant.withValues(alpha: 0.8)];
        text = 'Terminé';
        break;
      case ProjectStatus.delivered:
        gradientColors = [AppTheme.greenVibrant, AppTheme.greenVibrant.withValues(alpha: 0.8)];
        text = 'Livré';
        break;
      case ProjectStatus.cancelled:
        gradientColors = [AppTheme.pinkVibrant, AppTheme.pinkVibrant.withValues(alpha: 0.8)];
        text = 'Annulé';
        break;
      case ProjectStatus.archived:
        gradientColors = [AppTheme.textSecondaryColor, AppTheme.textSecondaryColor.withValues(alpha: 0.8)];
        text = 'Archivé';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: gradientColors[0].withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.3,
        ),
      ),
    );
  }

  Widget _buildProgressBar(double progress) {
    Color progressColor;
    if (progress < 0.3) {
      progressColor = AppTheme.pinkVibrant;
    } else if (progress < 0.7) {
      progressColor = AppTheme.orangeVibrant;
    } else {
      progressColor = AppTheme.greenVibrant;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progression',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: progressColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w700,
                  color: progressColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: AppTheme.borderColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: 8,
            ),
          ),
        ),
      ],
    );
  }
}

class ProjectsGrid extends StatelessWidget {
  final List<Project> projects;
  final Function(Project)? onProjectTap;
  final Function(Project)? onProjectDuplicate;

  const ProjectsGrid({
    super.key,
    required this.projects,
    this.onProjectTap,
    this.onProjectDuplicate,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // 2 projets par ligne
        childAspectRatio: 0.8, // Ratio largeur/hauteur
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return ProjectCard(
          project: project,
          onTap: onProjectTap != null ? () => onProjectTap!(project) : null,
          onDuplicate:
              onProjectDuplicate != null
                  ? () => onProjectDuplicate!(project)
                  : null,
        );
      },
    );
  }
}
