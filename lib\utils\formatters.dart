import 'package:intl/intl.dart';
import 'package:flutter/material.dart';

class Formatters {
  // Formatage de date (jour mois année)
  static String formatDate(DateTime date) {
    return DateFormat.yMMMMd('fr_FR').format(date);
  }

  // Formatage de date courte (jj/mm/yyyy)
  static String formatShortDate(DateTime date) {
    return DateFormat('dd/MM/yyyy', 'fr_FR').format(date);
  }

  // Formatage de plage de dates (23 août 2025 - 30 août 2025)
  static String formatDateRange(DateTime start, DateTime end) {
    final startFormat = DateFormat.d('fr_FR').format(start);
    final endFormat = DateFormat.d('fr_FR').format(end);
    final monthFormat = DateFormat.MMMM('fr_FR').format(end);
    final yearFormat = DateFormat.y('fr_FR').format(end);
    
    return '$startFormat - $endFormat $monthFormat $yearFormat';
  }

  // Formatage d'heure (10:30)
  static String formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Formatage de montant (1 250,00 €)
  static String formatCurrency(double amount) {
    return NumberFormat.currency(locale: 'fr_FR', symbol: '€').format(amount);
  }

  // Formatage de pourcentage (42%)
  static String formatPercent(double value) {
    return NumberFormat.percentPattern('fr_FR').format(value);
  }

  // Formatage de nombre avec séparateur de milliers (1 250)
  static String formatNumber(num number) {
    return NumberFormat.decimalPattern('fr_FR').format(number);
  }

  // Obtenir le nom du jour de la semaine
  static String getDayName(DateTime date) {
    return DateFormat.EEEE('fr_FR').format(date);
  }

  // Obtenir le nom court du jour de la semaine (Lun, Mar, etc.)
  static String getShortDayName(DateTime date) {
    return DateFormat.E('fr_FR').format(date);
  }

  // Obtenir le mois et l'année (août 2025)
  static String getMonthYear(DateTime date) {
    return DateFormat.yMMMM('fr_FR').format(date);
  }

  // Formatage de durée relative (il y a 2 jours, dans 3 heures, etc.)
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return 'il y a ${(difference.inDays / 365).floor()} an(s)';
    } else if (difference.inDays > 30) {
      return 'il y a ${(difference.inDays / 30).floor()} mois';
    } else if (difference.inDays > 0) {
      return 'il y a ${difference.inDays} jour(s)';
    } else if (difference.inHours > 0) {
      return 'il y a ${difference.inHours} heure(s)';
    } else if (difference.inMinutes > 0) {
      return 'il y a ${difference.inMinutes} minute(s)';
    } else if (difference.inSeconds > 0) {
      return 'il y a ${difference.inSeconds} seconde(s)';
    } else if (difference.inSeconds < 0 && difference.inSeconds > -60) {
      return 'dans ${-difference.inSeconds} seconde(s)';
    } else if (difference.inMinutes < 0 && difference.inMinutes > -60) {
      return 'dans ${-difference.inMinutes} minute(s)';
    } else if (difference.inHours < 0 && difference.inHours > -24) {
      return 'dans ${-difference.inHours} heure(s)';
    } else if (difference.inDays < 0 && difference.inDays > -30) {
      return 'dans ${-difference.inDays} jour(s)';
    } else if (difference.inDays < 0 && difference.inDays > -365) {
      return 'dans ${(-difference.inDays / 30).floor()} mois';
    } else {
      return 'dans ${(-difference.inDays / 365).floor()} an(s)';
    }
  }
}